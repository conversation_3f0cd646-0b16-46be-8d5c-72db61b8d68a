/**
 * Script completo para gerar todas as páginas SEO
 * Medicamentos + Condutas + Sitemap
 */

import { execSync } from 'child_process';

async function buildComplete() {
  try {
    console.log('🚀 Iniciando build completo com SEO...');
    
    // Passo 1: Build normal do Vite
    console.log('\n🔨 Passo 1: Executando build do Vite...');
    execSync('npm run build', { stdio: 'inherit' });

    // Passo 2: Gerar páginas de medicamentos
    console.log('\n💊 Passo 2: Gerando páginas de medicamentos...');
    execSync('node scripts/simpleBuild.js', { stdio: 'inherit' });

    // Passo 3: Gerar páginas de condutas
    console.log('\n📋 Passo 3: Gerando páginas de condutas...');
    execSync('node scripts/generateConducts.js', { stdio: 'inherit' });

    console.log('\n🎉 Build completo finalizado!');
    console.log('📊 Resumo:');
    console.log('   💊 138 páginas de medicamentos');
    console.log('   📋 48 páginas de condutas');
    console.log('   🗺️ Sitemap com ~190 URLs');
    console.log('   🎯 SEO otimizado para todas as páginas');

  } catch (error) {
    console.error('\n❌ Erro no build completo:', error);
    process.exit(1);
  }
}

buildComplete();
