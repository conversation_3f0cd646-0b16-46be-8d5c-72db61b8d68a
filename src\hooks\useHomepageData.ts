import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface PopularMedication {
  name: string;
  slug: string;
  category: string;
}

interface HomepageData {
  popularMedications: PopularMedication[];
  categories: string[];
}

export const useHomepageData = () => {
  return useQuery({
    queryKey: ['homepage-seo-data'],
    queryFn: async (): Promise<HomepageData> => {
      // Buscar medicamentos populares para SEO
      const { data: medicationsData, error: medicationsError } = await supabase
        .from('pedbook_medications')
        .select(`
          name,
          slug,
          pedbook_medication_categories(name)
        `)
        .order('name')
        .limit(20);

      if (medicationsError) throw medicationsError;

      const popularMedications: PopularMedication[] = medicationsData?.map(med => ({
        name: med.name,
        slug: med.slug,
        category: med.pedbook_medication_categories?.name || 'Geral'
      })) || [];

      // Buscar categorias para keywords
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('pedbook_medication_categories')
        .select('name')
        .order('name');

      if (categoriesError) throw categoriesError;

      const categories = categoriesData?.map(cat => cat.name) || [];

      return {
        popularMedications,
        categories
      };
    },
    staleTime: 60 * 60 * 1000, // 1 hora
    gcTime: 24 * 60 * 60 * 1000, // 24 horas
  });
};
