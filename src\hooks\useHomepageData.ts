import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface HomepageStats {
  medicamentos: number;
  categorias: number;
  usuarios: number;
  prescricoes: number;
  blog_posts: number;
}

interface PopularMedication {
  name: string;
  slug: string;
  category: string;
}

interface HomepageData {
  stats: HomepageStats;
  popularMedications: PopularMedication[];
  categories: string[];
}

export const useHomepageData = () => {
  return useQuery({
    queryKey: ['homepage-data'],
    queryFn: async (): Promise<HomepageData> => {
      // Buscar estatísticas
      const { data: statsData, error: statsError } = await supabase.rpc('get_homepage_stats');
      
      if (statsError) {
        // Fallback: buscar dados manualmente
        const [medicamentosRes, categoriasRes, usuariosRes, prescricoesRes, blogRes] = await Promise.all([
          supabase.from('pedbook_medications').select('id', { count: 'exact', head: true }),
          supabase.from('pedbook_medication_categories').select('id', { count: 'exact', head: true }),
          supabase.from('profiles').select('id', { count: 'exact', head: true }),
          supabase.from('pedbook_prescriptions').select('id', { count: 'exact', head: true }),
          supabase.from('pedbook_blog_posts').select('id', { count: 'exact', head: true }).eq('published', true)
        ]);

        const stats: HomepageStats = {
          medicamentos: medicamentosRes.count || 0,
          categorias: categoriasRes.count || 0,
          usuarios: usuariosRes.count || 0,
          prescricoes: prescricoesRes.count || 0,
          blog_posts: blogRes.count || 0
        };
      }

      // Buscar medicamentos populares
      const { data: medicationsData, error: medicationsError } = await supabase
        .from('pedbook_medications')
        .select(`
          name,
          slug,
          pedbook_medication_categories(name)
        `)
        .order('name')
        .limit(20);

      if (medicationsError) throw medicationsError;

      const popularMedications: PopularMedication[] = medicationsData?.map(med => ({
        name: med.name,
        slug: med.slug,
        category: med.pedbook_medication_categories?.name || 'Geral'
      })) || [];

      // Buscar categorias
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('pedbook_medication_categories')
        .select('name')
        .order('name');

      if (categoriesError) throw categoriesError;

      const categories = categoriesData?.map(cat => cat.name) || [];

      return {
        stats: statsData || {
          medicamentos: 138,
          categorias: 18,
          usuarios: 3560,
          prescricoes: 487,
          blog_posts: 2
        },
        popularMedications,
        categories
      };
    },
    staleTime: 60 * 60 * 1000, // 1 hora
    gcTime: 24 * 60 * 60 * 1000, // 24 horas
  });
};
