
import React from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { LoxoscelicFlowchart as LoxoscelicFlowchartComponent } from "@/components/flowcharts/loxoscelic/LoxoscelicFlowchart";

const LoxoscelicFlowchart = () => {
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-amber-50 via-white to-amber-50 dark:from-amber-900/20 dark:via-slate-900 dark:to-amber-900/10">
      <HelmetWrapper>
        <title>PedBook | Acidente Loxoscélico</title>
        <meta name="description" content="Fluxograma para manejo de acidentes com aranha marrom em pediatria" />
      </HelmetWrapper>
      
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Link 
              to="/flowcharts/venomous" 
              className="inline-flex items-center gap-2 text-amber-600 hover:text-amber-700 dark:text-amber-400 dark:hover:text-amber-300 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Voltar para Animais Peçonhentos</span>
            </Link>
          </div>

          <div className="text-center space-y-4">
            <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-amber-600 to-amber-800 dark:from-amber-400 dark:to-amber-600">
              Acidente Loxoscélico (Aranha Marrom)
            </h1>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Fluxograma para manejo de acidentes com aranha marrom em pediatria
            </p>
          </div>

          <LoxoscelicFlowchartComponent />
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default LoxoscelicFlowchart;
