
import React from "react";
import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { VaccineAgeGroup } from "./VaccineAgeGroup";
import { VaccineDose } from "./types";
import { Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { VaccineMetaTags } from "@/components/childcare/vaccine/VaccineMetaTags";

const formatAgeDisplay = (ageRecommendation: string) => {
  if (ageRecommendation === "0" || ageRecommendation.toLowerCase() === "ao nascer") {
    return "Ao nascer";
  }

  const months = parseInt(ageRecommendation.replace(/[^0-9]/g, ''));
  
  if (isNaN(months)) return ageRecommendation;

  if (months < 12) {
    return `${months} ${months === 1 ? 'mês' : 'meses'}`;
  } else {
    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;

    if (remainingMonths === 0) {
      return `${years} ${years === 1 ? 'ano' : 'anos'}`;
    } else {
      return `${years} ${years === 1 ? 'ano' : 'anos'} e ${remainingMonths} ${remainingMonths === 1 ? 'mês' : 'meses'}`;
    }
  }
};

const convertAgeToMonths = (ageRecommendation: string): number => {
  if (ageRecommendation === "0" || ageRecommendation.toLowerCase() === "ao nascer") {
    return 0;
  }
  
  const months = parseInt(ageRecommendation.replace(/[^0-9]/g, ''));
  return isNaN(months) ? 999999 : months;
};

const VaccinesPage = () => {
  const { data: doses = [], isLoading } = useQuery({
    queryKey: ['vaccine-doses'],
    queryFn: async () => {
      const { data: allDoses, error: dosesError } = await supabase
        .from('pedbook_vaccine_doses')
        .select(`
          id,
          dose_number,
          age_recommendation,
          description,
          type,
          dose_type,
          vaccine_id,
          vaccine:pedbook_vaccines(
            id,
            name,
            description
          )
        `)
        .order('age_recommendation');

      if (dosesError) throw dosesError;

      const { data: relationships, error: relError } = await supabase
        .from('pedbook_vaccine_relationships')
        .select(`
          parent_vaccine_id,
          child_vaccine:pedbook_vaccines!child_vaccine_id (
            id,
            name
          )
        `);

      if (relError) throw relError;

      const relationshipMap: Record<string, any[]> = {};
      relationships.forEach(rel => {
        if (!relationshipMap[rel.parent_vaccine_id]) {
          relationshipMap[rel.parent_vaccine_id] = [];
        }
        relationshipMap[rel.parent_vaccine_id].push(rel.child_vaccine);
      });

      const dosesWithRelated = allDoses.map((dose: any) => ({
        ...dose,
        related_vaccines: relationshipMap[dose.vaccine_id] || []
      }));

      return dosesWithRelated.sort((a: VaccineDose, b: VaccineDose) => {
        const monthsA = convertAgeToMonths(a.age_recommendation);
        const monthsB = convertAgeToMonths(b.age_recommendation);
        return monthsA - monthsB;
      });
    },
  });

  const groupedDoses = React.useMemo(() => {
    const emptyGroup: Record<string, VaccineDose[]> = {};
    if (!doses) return emptyGroup;
    
    return doses.reduce<Record<string, VaccineDose[]>>((acc, dose) => {
      const formattedAge = formatAgeDisplay(dose.age_recommendation);
      if (!acc[formattedAge]) {
        acc[formattedAge] = [];
      }
      acc[formattedAge].push(dose);
      return acc;
    }, emptyGroup);
  }, [doses]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-full">
            <div className="animate-pulse text-gray-500 dark:text-gray-400">Carregando vacinas...</div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col dark:bg-slate-900">
      <VaccineMetaTags />
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <Link 
          to="/puericultura" 
          className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors mb-8 dark:text-blue-400 dark:hover:text-blue-300"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Voltar para Puericultura</span>
        </Link>

        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-12"
        >
          <div className="max-w-3xl mx-auto bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 p-8 rounded-2xl shadow-lg border border-transparent dark:border-gray-700">
            <h1 className="text-3xl font-bold text-center bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400">
              Calendário Vacinal
            </h1>
            <p className="text-center mt-2 text-gray-600 dark:text-gray-300">
              Acompanhe todas as vacinas recomendadas para cada fase
            </p>
          </div>
        </motion.div>
        
        <div className="max-w-3xl mx-auto">
          <div className="relative">
            <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-emerald-400 to-emerald-600 dark:from-emerald-600 dark:to-emerald-800 rounded-full" />

            {Object.entries(groupedDoses).map(([age, doses], index) => (
              <VaccineAgeGroup
                key={age}
                age={age}
                doses={doses}
                index={index}
              />
            ))}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default VaccinesPage;
