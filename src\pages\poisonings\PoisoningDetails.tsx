
import { useParams, useNavigate } from "react-router-dom";
import { toxidromes } from "@/data/toxidromes";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";
import { useState } from "react";
import { DoseCalculator } from "@/components/poisonings/DoseCalculator";
import { ToxidromeDetails } from "@/components/poisonings/ToxidromeDetails";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { getThemeClasses } from "@/components/ui/theme-utils";

const PoisoningDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const toxidrome = toxidromes.find((t) => t.id === id);
  const [calculatedDose, setCalculatedDose] = useState<string | null>(null);

  if (!toxidrome) {
    return (
      <div className={getThemeClasses.pageBackground()}>
        <HelmetWrapper>
          <title>Intoxicação não encontrada | PedBook</title>
          <meta name="description" content="Intoxicação não encontrada no PedBook." />
          <meta property="og:title" content="Intoxicação não encontrada | PedBook" />
          <meta property="og:description" content="Intoxicação não encontrada no PedBook." />
          <meta property="og:type" content="website" />
          <meta property="og:url" content={`https://pedb.com.br/poisonings/${id}`} />
        </HelmetWrapper>
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Intoxicação não encontrada</h1>
            <Button onClick={() => navigate("/poisonings")} className="mt-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Voltar
            </Button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  const pageTitle = `Intoxicação por ${toxidrome.name} | PedBook`;
  const pageDescription = `Informações sobre intoxicação por ${toxidrome.name.toLowerCase()}, incluindo antídoto (${toxidrome.antidote}), precauções e manejo clínico.`;

  return (
    <div className={getThemeClasses.pageBackground()}>
      <HelmetWrapper>
        <title>{pageTitle}</title>
        <meta name="description" content={pageDescription} />
        <meta property="og:title" content={pageTitle} />
        <meta property="og:description" content={pageDescription} />
        <meta property="og:type" content="website" />
        <meta property="og:url" content={`https://pedb.com.br/poisonings/${id}`} />
      </HelmetWrapper>
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8">
        <Button
          variant="outline"
          onClick={() => navigate("/poisonings")}
          className="mb-6 hover:scale-105 transition-transform dark:text-gray-200 dark:border-gray-700 dark:hover:bg-slate-700"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Voltar
        </Button>

        <div className="max-w-4xl mx-auto space-y-6">
          <Card className="overflow-hidden border-0 shadow-lg animate-fade-in dark:bg-slate-800 dark:border-slate-700">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-700 dark:to-purple-700 p-6">
              <h1 className="text-3xl md:text-4xl font-bold text-white">
                {toxidrome.name}
              </h1>
              <p className="text-blue-100 mt-2 text-lg">
                Toxíndrome: {toxidrome.type}
              </p>
            </div>
            <CardContent className="p-6 space-y-6 dark:bg-slate-800">
              <ToxidromeDetails toxidrome={toxidrome} />

              <div className={getThemeClasses.glassContainer("p-6 animate-fade-in delay-200")}>
                <DoseCalculator 
                  toxidrome={toxidrome}
                  onCalculate={setCalculatedDose}
                />

                {calculatedDose && (
                  <div className="mt-4 p-6 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-blue-100 dark:border-blue-900/50 animate-fade-in">
                    <h4 className="font-medium text-blue-900 dark:text-blue-300 mb-2">Dose Calculada:</h4>
                    <pre className="whitespace-pre-wrap text-gray-700 dark:text-gray-300 font-medium">
                      {calculatedDose}
                    </pre>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default PoisoningDetails;
