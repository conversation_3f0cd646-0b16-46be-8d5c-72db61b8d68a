import React, { useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { supabase } from '@/integrations/supabase/client';
import { FileText, ArrowLeft, AlertCircle, Loader2, ChevronRight, Package, Pill, Info, Clipboard, List, ExternalLink } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { MedicationFeedback } from '@/components/medication/MedicationFeedback';

const MedicationInstructionPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();

  // Fetch medication data
  const { data: medication, isLoading: loadingMedication, error: medicationError } = useQuery({
    queryKey: ["medication-instruction-details", slug],
    queryFn: async () => {
      if (!slug) return null;

      const { data, error } = await supabase
        .from("pedbook_medications")
        .select(`
          id,
          name,
          description,
          brands,
          slug,
          pedbook_medication_categories(name)
        `)
        .eq("slug", slug)
        .maybeSingle();

      if (error) {
        if (error.code === "PGRST116") {
          return null;
        }
        throw error;
      }

      return data;
    },
    enabled: !!slug,
  });

  // Fetch medication instructions
  const { data: instructions, isLoading: loadingInstructions, error: instructionsError } = useQuery({
    queryKey: ["medication-instructions-detail", medication?.id],
    queryFn: async () => {
      if (!medication?.id) return null;

      const { data, error } = await supabase
        .from("pedbook_medication_instructions")
        .select("*")
        .eq("medication_id", medication.id)
        .eq("is_published", true)
        .maybeSingle();

      if (error) {
        // Don't throw error if no instructions found
        if (error.code === "PGRST116") {
          return null;
        }
        throw error;
      }

      return data;
    },
    enabled: !!medication?.id
  });

  // Determine the format type
  const formatType = instructions?.format_type === 'simple' ? 'simple' : 'standard';

  useEffect(() => {
    // Set up image click handler for previewing images
    window.handleImageClick = (src: string) => {
      window.open(src, '_blank');
    };

    return () => {
      // Clean up when component unmounts
      delete window.handleImageClick;
    };
  }, []);

  // Function to decode HTML entities
  const decodeHtmlEntities = (text: string) => {
    const textarea = document.createElement('textarea');
    textarea.innerHTML = text;
    return textarea.value;
  };

  // Function to process HTML content in standard format
  const processStandardContent = (content: string) => {
    if (!content) return '';

    // Process titles with format "##. Title"
    return decodeHtmlEntities(content
      .replace(/<h2><strong>##\.\s*(.*?)<\/strong><\/h2>/g, '<h2 class="text-xl font-bold text-blue-600 dark:text-blue-400 mt-5 mb-2">$1</h2>')
      .replace(/<h3><strong>##\.\s*(.*?)<\/strong><\/h3>/g, '<h3 class="text-lg font-semibold text-blue-500 dark:text-blue-300 mt-3 mb-1">$1</h3>')
      // Process subtitles with format "##; Subtitle"
      .replace(/<strong>##;\s*(.*?)<\/strong>/g, '<h4 class="text-base font-semibold text-blue-500 dark:text-blue-300 mt-2 mb-1">$1</h4>')
      .replace(/<h4><strong>(.*?)<\/strong><\/h4>/g, '<h4 class="text-base font-semibold text-blue-500 dark:text-blue-300 mt-2 mb-1">$1</h4>')
      .replace(/<strong>(.*?)<\/strong>/g, '<span class="font-semibold">$1</span>')
      .replace(/<p>/g, '<p class="mb-2">')
      .replace(/<ul>/g, '<ul class="list-disc pl-5 mb-2 space-y-1">')
      .replace(/<li><p>/g, '<li class="mb-1">')
      .replace(/<a([^>]*)target="_blank"([^>]*)>/g, '<a$1$2>')
      .replace(/<a([^>]*)rel="noopener noreferrer"([^>]*)>/g, '<a$1$2>')
      .replace(/<a([^>]*)href="([^"]*)"([^>]*)>/g, '<a$1href="$2"$3 target="_self">')
      .replace(/<img([^>]*)>/g, '<img$1 class="max-w-full h-auto rounded-lg my-3 cursor-pointer hover:opacity-90 transition-opacity" onclick="window.handleImageClick && window.handleImageClick(this.src)">')
    );
  };

  // Function to process HTML content in simple format
  const processSimpleContent = (content: string) => {
    if (!content) return '';

    // Primeiro, vamos extrair os marcadores ##. e ##; mesmo quando estão dentro de tags HTML
    // Procurar por ##. em qualquer lugar do HTML, incluindo dentro de tags
    let processedContent = content.replace(
      /(<[^>]*>)*##\.\s*(.*?)(<\/[^>]*>)*/g,
      (match, openTag, title) => {
        // Remover tags HTML do título
        const cleanTitle = title.replace(/<\/?[^>]+(>|$)/g, "");
        return `<h2 class="text-xl font-bold text-blue-600 dark:text-blue-400 mt-5 mb-2">${cleanTitle}</h2>`;
      }
    );

    // Remover qualquer ocorrência restante de ##. e seu texto até a próxima quebra de linha
    processedContent = processedContent.replace(/##\.\s*[^<\n\r]*(?:<br>|<\/p>|$)/g, '');

    // Procurar por ##; em qualquer lugar do HTML, incluindo dentro de tags
    processedContent = processedContent.replace(
      /(<[^>]*>)*##;\s*(.*?)(<\/[^>]*>)*/g,
      (match, openTag, subtitle) => {
        // Remover tags HTML do subtítulo
        const cleanSubtitle = subtitle.replace(/<\/?[^>]+(>|$)/g, "");
        return `<h4 class="text-base font-semibold text-blue-500 dark:text-blue-300 mt-2 mb-1">${cleanSubtitle}</h4>`;
      }
    );

    // Remover qualquer ocorrência restante de ##; e seu texto até a próxima quebra de linha
    processedContent = processedContent.replace(/##;\s*[^<\n\r]*(?:<br>|<\/p>|$)/g, '');



    // Primeiro, vamos identificar todas as listas no conteúdo
    const ulRegex = /<ul>([\s\S]*?)<\/ul>/g;
    const ulMatches = Array.from(processedContent.matchAll(ulRegex));


    // Para cada lista encontrada, vamos aplicar uma formatação específica
    ulMatches.forEach((match, index) => {
      const originalUl = match[0];
      const ulContent = match[1];


      // Aplicar formatação específica para esta lista
      let formattedUl = '<ul class="list-disc pl-5 mb-2 space-y-1">';

      // Dividir o conteúdo da lista em itens
      const liRegex = /<li>([\s\S]*?)<\/li>/g;
      const liMatches = Array.from(ulContent.matchAll(liRegex));


      // Processar cada item da lista
      liMatches.forEach((liMatch, liIndex) => {
        const liContent = liMatch[1];

        // Remover espaços extras e quebras de linha no início e fim do conteúdo
        const trimmedContent = liContent.trim();


        // Remover as tags <p> dentro dos itens de lista
        let cleanContent = trimmedContent;
        if (cleanContent.startsWith('<p>') && cleanContent.endsWith('</p>')) {
          cleanContent = cleanContent.substring(3, cleanContent.length - 4);
        }

        // Adicionar o item formatado à lista
        formattedUl += `<li class="mb-1">${cleanContent}</li>`;
      });

      // Fechar a lista formatada
      formattedUl += '</ul>';

      // Substituir a lista original pela formatada
      processedContent = processedContent.replace(originalUl, formattedUl);
    });

    // Garantir que não haja <br> extras no final do conteúdo
    processedContent = processedContent.replace(/<br>$/g, '');

    // Corrigir listas aninhadas - remover tags <p> em listas aninhadas
    processedContent = processedContent.replace(/<ul><li><p>(.*?)<\/p><ul>/g, '<ul><li>$1<ul>');

    // Corrigir problema com listas quebradas
    processedContent = processedContent.replace(/<\/li><\/ul><\/li><li>/g, '</li></ul></li><li>');

    // Corrigir problema com tags <p> dentro de <li> em listas aninhadas
    processedContent = processedContent.replace(/<li><ul><li><p>(.*?)<\/p>/g, '<li><ul><li>$1');

    // Corrigir problema com tags <p> em itens de lista
    processedContent = processedContent.replace(/<li><p>(.*?)<\/p><\/li>/g, '<li>$1</li>');

    // Corrigir problema específico com itens de lista seguidos por marcadores ##;
    processedContent = processedContent.replace(/<\/li><\/ul><p>\s*##;/g, '</li></ul><p class="mt-4">##;');

    // Corrigir problema com <p> vazio após </li>
    processedContent = processedContent.replace(/<\/li><p><\/p>/g, '</li>');

    // Corrigir problema com <p> vazio após </ul>
    processedContent = processedContent.replace(/<\/ul><p><\/p>/g, '</ul>');

    // Corrigir problema com tags <p> vazias que podem estar causando espaços extras
    processedContent = processedContent.replace(/<p>\s*<\/p>/g, '');

    // Corrigir problema com <li> vazio
    processedContent = processedContent.replace(/<li>\s*<\/li>/g, '');

    // Corrigir problema específico com itens de lista que têm quebra de linha entre o marcador e o texto
    // Este é o problema que faz com que a bolinha do markdown apareça em uma linha e o texto na linha seguinte
    processedContent = processedContent.replace(/<li class="mb-1">\s*<br>\s*/g, '<li class="mb-1">');
    processedContent = processedContent.replace(/<li>\s*<br>\s*/g, '<li>');

    // Corrigir problema com tags <p> dentro de <li> que podem estar causando espaços extras
    processedContent = processedContent.replace(/<li class="mb-1"><p>\s*(.*?)\s*<\/p><\/li>/g, '<li class="mb-1">$1</li>');
    processedContent = processedContent.replace(/<li><p>\s*(.*?)\s*<\/p><\/li>/g, '<li>$1</li>');

    // Corrigir problema específico com "Infecção secundária" que aparece após uma lista
    processedContent = processedContent.replace(/<\/li><li><p>Infecção secundária<\/p>/g, '</li><li>Infecção secundária');

    // Corrigir problema com quebras de linha dentro de itens de lista
    processedContent = processedContent.replace(/<li class="mb-1">(.*?)<br>/g, '<li class="mb-1">$1 ');
    processedContent = processedContent.replace(/<li>(.*?)<br>/g, '<li>$1 ');

    // Corrigir problema específico com espaços antes de ##; em parágrafos
    processedContent = processedContent.replace(/<p>\s*##;/g, '<p>##;');

    // Corrigir problema com itens de lista vazios seguidos de texto
    processedContent = processedContent.replace(/<li class="mb-1"><\/li>\s*([^<]+)/g, '<li class="mb-1">$1</li>');
    processedContent = processedContent.replace(/<li><\/li>\s*([^<]+)/g, '<li>$1</li>');

    // Corrigir problema específico com espaços entre listas e parágrafos
    processedContent = processedContent.replace(/<\/ul><p>\s+/g, '</ul><p>');

    // Remover itens de lista vazios
    processedContent = processedContent.replace(/<li class="mb-1"><\/li>/g, '');
    processedContent = processedContent.replace(/<li><\/li>/g, '');

    // Corrigir problema específico com ##; Sistêmicas (raras): após uma lista
    processedContent = processedContent.replace(/<\/ul><p>\s*##;\s*Sistêmicas\s*\(raras\):/g, '</ul><p class="mt-4">##; Sistêmicas (raras):');

    // Corrigir problema com quebras de linha entre itens de lista
    processedContent = processedContent.replace(/<\/li>\s*<br>\s*<li/g, '</li><li');

    // Corrigir problema com espaços extras no início de itens de lista
    processedContent = processedContent.replace(/<li class="mb-1">\s+/g, '<li class="mb-1">');
    processedContent = processedContent.replace(/<li>\s+/g, '<li>');

    // Corrigir problema com espaços extras após as tags <li>
    processedContent = processedContent.replace(/<li([^>]*)>\s+/g, '<li$1>');

    // Corrigir problema específico com ##; após uma lista
    processedContent = processedContent.replace(/<\/ul><p>\s*##;/g, '</ul><p class="mt-4">##;');

    // Corrigir problema específico com espaços no início de parágrafos após listas
    processedContent = processedContent.replace(/<\/ul><p>\s+/g, '</ul><p>');

    // Corrigir problema específico com espaços no início de itens de lista
    processedContent = processedContent.replace(/<li([^>]*)>\s+/g, '<li$1>');

    // Corrigir problema específico com espaços no início de itens de lista com classe mb-1
    processedContent = processedContent.replace(/<li class="mb-1">\s+/g, '<li class="mb-1">');

    // Corrigir problema específico com espaços no início de itens de lista sem classe
    processedContent = processedContent.replace(/<li>\s+/g, '<li>');

    // Corrigir problema específico com espaços no final de itens de lista
    processedContent = processedContent.replace(/\s+<\/li>/g, '</li>');

    // Corrigir problema específico com quebras de linha dentro de itens de lista
    processedContent = processedContent.replace(/<li([^>]*)>(.*?)<br>(.*?)<\/li>/g, '<li$1>$2 $3</li>');

    // Corrigir problema específico com ##; Sistêmicas (raras): após uma lista
    processedContent = processedContent.replace(/<\/ul><p>\s*##;\s*Sistêmicas\s*\(raras\):/g, '</ul><p class="mt-4">##; Sistêmicas (raras):');

    // Corrigir problema específico com espaços antes de ##; em parágrafos
    processedContent = processedContent.replace(/<p>\s*##;/g, '<p>##;');

    // Corrigir problema específico com espaços antes de qualquer texto após ##;
    processedContent = processedContent.replace(/##;\s+/g, '##; ');

    // Corrigir problema específico com ##; seguido de espaço e texto
    processedContent = processedContent.replace(/##;\s+([A-Za-zÀ-ÖØ-öø-ÿ])/g, '##; $1');

    // Corrigir problema específico com espaços no início de itens de lista após <ul>
    processedContent = processedContent.replace(/<ul[^>]*>\s*<li/g, '<ul><li');

    // Corrigir problema específico com espaços entre </li> e <li>
    processedContent = processedContent.replace(/<\/li>\s+<li/g, '</li><li');

    // Corrigir problema específico com espaços entre </ul> e <p>
    processedContent = processedContent.replace(/<\/ul>\s+<p/g, '</ul><p');

// Remover espaços extras e tags <p> no início de cada item de lista
processedContent = processedContent.replace(/<li([^>]*)>([\s]*)(.*?)<\/li>/g, (match, attrs, spaces, content) => {
  // Remover as tags <p> dentro dos itens de lista
  let cleanContent = content.trim();
  if (cleanContent.startsWith('<p>') && cleanContent.endsWith('</p>')) {
    cleanContent = cleanContent.substring(3, cleanContent.length - 4);
  }

  return `<li${attrs}>${cleanContent}</li>`;
});


// Corrigir problema específico com os subtítulos que têm espaços extras
// Isso afeta os subtítulos como "##; Locais (frequentes):" que aparecem com espaço inicial
processedContent = processedContent.replace(/<h4 class="[^"]*">\s+/g, '<h4 class="text-base font-semibold text-blue-500 dark:text-blue-300 mt-2 mb-1">');

// Remover espaços extras no início de cada subtítulo
processedContent = processedContent.replace(/<h4([^>]*)>([\s]*)(.*?)<\/h4>/g, (match, attrs, spaces, content) => {
  return `<h4${attrs}>${content.trim()}</h4>`;
});



    // Agora aplicamos as formatações básicas para elementos HTML comuns
    processedContent = decodeHtmlEntities(processedContent
      .replace(/<strong>(.*?)<\/strong>/g, '<span class="font-semibold">$1</span>')
      .replace(/<p>/g, '<p class="mb-2">')
      .replace(/<li><p>/g, '<li class="mb-1">')
      .replace(/<a([^>]*)target="_blank"([^>]*)>/g, '<a$1$2>')
      .replace(/<a([^>]*)rel="noopener noreferrer"([^>]*)>/g, '<a$1$2>')
      .replace(/<a([^>]*)href="([^"]*)"([^>]*)>/g, '<a$1href="$2"$3 target="_self">')
      .replace(/<img([^>]*)>/g, '<img$1 class="max-w-full h-auto rounded-lg my-3 cursor-pointer hover:opacity-90 transition-opacity" onclick="window.handleImageClick && window.handleImageClick(this.src)">')
    );

    return processedContent;
  };

  // Function to process HTML content
  const processContent = (content: string) => {
    if (!content) return '';

    return formatType === 'simple'
      ? processSimpleContent(content)
      : processStandardContent(content);
  };

  // Function to extract sections and subsections from content
  const extractSectionsFromContent = (content: string) => {
    if (!content) return [];

    const sections = [];

    if (formatType !== 'simple') {
      // Standard format processing
      const standardMainSectionRegex = /(?:<h[2-4]><strong>##\.\s*(.*?)<\/strong><\/h[2-4]>|<h2>##\.\s*(.*?)<\/h2>|<h2><strong>##\.\s*<\/strong>(.*?)<\/h2>)([\s\S]*?)(?=<h[2-4]><strong>##\.|<h2>##\.|$)/g;

      const mainSections = Array.from(content.matchAll(standardMainSectionRegex));

      if (mainSections.length === 0) {
        // If no formatted sections, treat as a single section
        return [{
          title: "Informações Gerais",
          content: content,
          subsections: []
        }];
      }

      // Process sections in standard format
      mainSections.forEach((sectionMatch) => {
        const title = (sectionMatch[1] || sectionMatch[2] || sectionMatch[3] || "").trim();
        let mainContent = sectionMatch[4] || "";

        // Extract subsections (with ##; format)
        const subsections = [];
        const standardSubsectionRegex = /<strong>##;\s*(.*?)<\/strong>([\s\S]*?)(?=<strong>##;|<h[2-4]><strong>##\.|$)/g;
        const subsectionMatches = Array.from(mainContent.matchAll(standardSubsectionRegex));

        // Process each subsection found
        if (subsectionMatches.length > 0) {
          // Clear main content if subsections exist
          const firstSubsectionStart = mainContent.indexOf(subsectionMatches[0][0]);
          if (firstSubsectionStart !== -1) {
            mainContent = mainContent.substring(0, firstSubsectionStart).trim();
          }

          subsectionMatches.forEach((subMatch) => {
            const subTitle = subMatch[1].trim();
            const subContent = subMatch[2].trim();

            subsections.push({
              title: subTitle,
              content: subContent
            });
          });
        }

        sections.push({
          title,
          content: mainContent,
          subsections
        });
      });
    } else {
      // Simple format processing
      // Abordagem mais direta para extrair seções
      // Primeiro, vamos extrair todas as seções principais usando uma expressão regular mais robusta
      const sectionRegex = /##\.\s*([^<\n\r]*?)(?:<br>|<\/p>|<p>|$)([\s\S]*?)(?=##\.|$)/g;
      const sectionMatches = Array.from(content.matchAll(sectionRegex));

      if (sectionMatches.length === 0) {
        // Se não encontrou seções, retornar todo o conteúdo como uma única seção
        return [{
          title: "Informações Gerais",
          content: content,
          subsections: []
        }];
      }

      // Processar cada seção encontrada
      sectionMatches.forEach((match, index) => {
        const title = match[1].trim();
        let sectionContent = match[2] || '';

        // Extrair subseções
        const subsections = [];
        const subsectionRegex = /##;\s*([^<\n\r]*?)(?:<br>|<\/p>|<p>|$)([\s\S]*?)(?=##;|##\.|$)/g;
        const subsectionMatches = Array.from(sectionContent.matchAll(subsectionRegex));

        if (subsectionMatches.length > 0) {
          // Se encontrou subseções, limpar o conteúdo principal para não duplicar
          const firstSubsectionStart = sectionContent.indexOf(subsectionMatches[0][0]);
          if (firstSubsectionStart !== -1) {
            sectionContent = sectionContent.substring(0, firstSubsectionStart).trim();
          }

          // Processar cada subseção
          subsectionMatches.forEach((subMatch) => {
            const subTitle = subMatch[1].trim();
            const subContent = subMatch[2].trim();

            subsections.push({
              title: subTitle,
              content: subContent
            });
          });
        }

        // Adicionar a seção processada
        sections.push({
          title,
          content: sectionContent,
          subsections
        });
      });
    }

    // If no sections were found, return a general section with all content
    if (sections.length === 0) {
      return [{
        title: "Informações Gerais",
        content: content,
        subsections: []
      }];
    }

    return sections;
  };

  const isLoading = loadingMedication || loadingInstructions;
  const error = medicationError || instructionsError;

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  // Icons for different section types
  const getSectionIcon = (title: string) => {
    const lowerTitle = title.toLowerCase();
    if (lowerTitle.includes('indicaç')) return <List className="h-5 w-5" />;
    if (lowerTitle.includes('posolog')) return <Clipboard className="h-5 w-5" />;
    if (lowerTitle.includes('apresenta')) return <Package className="h-5 w-5" />;
    if (lowerTitle.includes('composiç')) return <Pill className="h-5 w-5" />;
    if (lowerTitle.includes('contra')) return <AlertCircle className="h-5 w-5" />;
    if (lowerTitle.includes('precau')) return <Info className="h-5 w-5" />;
    return <FileText className="h-5 w-5" />;
  };

  // Function to check if the title and category should be on the same line
  const shouldShowInline = () => {
    // If medication name is less than 20 chars, show inline
    return medication?.name && medication.name.length < 20;
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-800">
      <HelmetWrapper>
        <title>
          {medication ? `Bula: ${medication.name}` : 'Bula Profissional'} | PedBook
        </title>
        <meta
          name="description"
          content={`Informações completas sobre ${medication?.name || 'medicamento'} para profissionais de saúde.`}
        />
      </HelmetWrapper>

      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto">
          {/* Navigation with medication link */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 mb-6">
            <Button
              variant="ghost"
              onClick={() => navigate('/bulas-profissionais')}
              className="text-primary hover:bg-primary/5 -ml-2 flex items-center gap-2 justify-start"
            >
              <ArrowLeft className="h-4 w-4" />
              Voltar para lista de bulas
            </Button>
            {medication?.slug && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate(`/medicamentos/${medication.slug}`)}
                className="gap-2 border-primary/20 bg-white dark:bg-slate-700 shadow-sm hover:bg-primary/5 dark:hover:bg-primary/10 dark:text-white flex items-center justify-center sm:justify-start"
              >
                <Pill className="h-4 w-4 text-primary" />
                <span className="whitespace-nowrap">Ver Medicamento</span>
              </Button>
            )}
          </div>

          {isLoading ? (
            <div className="space-y-4">
              <div className="h-10 w-2/3 bg-slate-200 animate-pulse rounded-lg"></div>
              <div className="h-6 w-1/2 bg-slate-200 animate-pulse rounded-lg"></div>
              <div className="h-6 w-1/3 bg-slate-200 animate-pulse rounded-lg"></div>
            </div>
          ) : error ? (
            <Card className="border-destructive/50 bg-destructive/5 shadow-lg">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-destructive" />
                  Erro ao carregar bula
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p>Não foi possível carregar as informações desta bula. Tente novamente mais tarde.</p>
                <Button variant="outline" className="mt-4" onClick={() => window.location.reload()}>
                  Tentar novamente
                </Button>
              </CardContent>
            </Card>
          ) : (
            <>
              {/* Medication header - Simplified and centered version */}
              <motion.div
                className="mb-8 text-center"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                {shouldShowInline() ? (
                  // Inline display for shorter medication names
                  <div className="flex justify-center items-center flex-wrap gap-2 mb-2">
                    <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100">
                      {medication?.name}
                    </h1>
                    {medication?.pedbook_medication_categories?.name && (
                      <span className="bg-primary/5 border border-primary/10 px-3 py-1 rounded-lg text-sm text-primary">
                        Categoria: {medication.pedbook_medication_categories.name}
                      </span>
                    )}
                  </div>
                ) : (
                  // Stacked display for longer medication names
                  <>
                    <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-3">
                      {medication?.name}
                    </h1>

                    {medication?.pedbook_medication_categories?.name && (
                      <div className="inline-block bg-primary/5 border border-primary/10 px-4 py-2 rounded-lg">
                        <p className="text-sm text-primary">
                          Categoria: {medication.pedbook_medication_categories.name}
                        </p>
                      </div>
                    )}
                  </>
                )}
              </motion.div>

              {instructions?.content ? (
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                  className="space-y-8"
                >
                  {/* Content sections */}
                  <motion.div variants={itemVariants} className="mt-8">
                    <Accordion type="multiple" className="space-y-4">
                      {extractSectionsFromContent(instructions.content).map((section, index) => (
                        <AccordionItem
                          key={index}
                          value={`section-${index}`}
                          className="rounded-xl overflow-hidden border border-blue-100 bg-white/80 backdrop-blur-sm shadow-md transition-all hover:shadow-lg"
                        >
                          <AccordionTrigger className="px-6 py-4 hover:bg-blue-50/50 data-[state=open]:bg-blue-50/80 transition-colors duration-300">
                            <div className="flex items-center gap-3 text-left">
                              <span className="flex items-center justify-center rounded-full bg-primary text-white p-2">
                                {getSectionIcon(section.title)}
                              </span>
                              <div>
                                <h2 className="text-lg font-semibold text-gray-900">
                                  {section.title}
                                </h2>
                                {section.subsections.length > 0 && (
                                  <p className="text-xs text-gray-500 mt-1">
                                    {section.subsections.length} subtópicos
                                  </p>
                                )}
                              </div>
                            </div>
                          </AccordionTrigger>

                          <AccordionContent className="px-6 pt-3 pb-6 bg-white">
                            {section.content ? (
                              <div className="prose prose-blue prose-headings:text-primary prose-headings:font-semibold max-w-none mb-6">
                                <div
                                  dangerouslySetInnerHTML={{
                                    __html: processContent(section.content)
                                  }}
                                />
                              </div>
                            ) : null}

                            {section.subsections.length > 0 && (
                              <div className="space-y-4 mt-5">
                                {section.subsections.map((subsection, subIndex) => (
                                  <div
                                    key={subIndex}
                                    className="border border-blue-100 rounded-lg bg-blue-50/30 p-4"
                                  >
                                    <h3 className="text-md font-semibold text-primary mb-3 pb-2 border-b border-blue-100 flex items-center gap-2">
                                      <span className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-xs">
                                        {index + 1}.{subIndex + 1}
                                      </span>
                                      {subsection.title}
                                    </h3>
                                    <div className="prose prose-sm prose-blue max-w-none">
                                      <div
                                        dangerouslySetInnerHTML={{
                                          __html: processContent(subsection.content)
                                        }}
                                      />
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </AccordionContent>
                        </AccordionItem>
                      ))}
                    </Accordion>
                  </motion.div>

                  {/* Add Medication Feedback Component */}
                  <motion.div
                    variants={itemVariants}
                    className="mt-4"
                  >
                    {medication?.id && (
                      <MedicationFeedback
                        medicationId={medication.id}
                        medicationName={medication.name}
                      />
                    )}
                  </motion.div>
                </motion.div>
              ) : (
                <div className="text-center p-10 border rounded-lg bg-white shadow-sm">
                  <FileText className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                  <h2 className="text-xl font-semibold mb-2">
                    Bula não encontrada
                  </h2>
                  <p className="text-muted-foreground max-w-md mx-auto mb-6">
                    Não encontramos a bula profissional para este medicamento no momento.
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => navigate('/bulas-profissionais')}
                  >
                    Voltar para lista de bulas
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default MedicationInstructionPage;
