
import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { ChevronLeft } from "lucide-react";
import { Link } from "react-router-dom";
import { CapurroMetaTags } from "@/components/calculators/capurro/CapurroMetaTags";
import { getThemeClasses } from "@/components/ui/theme-utils";

interface CapurroValues {
  mamilo: number | null;
  pregas: number | null;
  pele: number | null;
  orelha: number | null;
  glandula: number | null;
}

const CapurroCalculator = () => {
  const [values, setValues] = useState<CapurroValues>({
    mamilo: null,
    pregas: null,
    pele: null,
    orelha: null,
    glandula: null,
  });

  const criteria = {
    mamilo: [
      { value: 0, label: "Apenas visível, sem aréola" },
      { value: 5, label: "Aréola bem definida - diâmetro menor que 0,75 cm" },
      { value: 10, label: "Aréola bem definida, borda não elevada - diâmetro maior que 0,75 cm" },
      { value: 15, label: "Borda elevada - diâmetro maior que 0,75 cm" },
    ],
    pregas: [
      { value: 0, label: "Sem pregas plantares" },
      { value: 5, label: "Marcas mal definidas na metade anterior" },
      { value: 10, label: "Marcas bem definidas na metade anterior e sulcos bem definidos no terço anterior" },
      { value: 15, label: "Sulcos na metade anterior da planta" },
      { value: 20, label: "Sulcos em mais da metade anterior da planta" },
    ],
    pele: [
      { value: 0, label: "Muito fina, gelatinosa" },
      { value: 5, label: "Fina e lisa" },
      { value: 10, label: "Algo mais grossa, discreta descamação superficial" },
      { value: 15, label: "Grossa, sulcos superficiais, descamação nas mãos e pés" },
      { value: 20, label: "Grossa apergaminhada, com sulcos profundos" },
    ],
    orelha: [
      { value: 0, label: "Chata disforme, pavilhão não encurvado" },
      { value: 8, label: "Pavilhão parcialmente encurvado na borda" },
      { value: 16, label: "Pavilhão parcialmente encurvado em toda parte superior" },
      { value: 24, label: "Pavilhão totalmente encurvado" },
    ],
    glandula: [
      { value: 0, label: "Não palpável" },
      { value: 5, label: "Palpável, menos de 0,5 cm" },
      { value: 10, label: "Entre 0,5 e 1 cm" },
      { value: 15, label: "Maior de 1 cm" },
    ],
  };

  const handleChange = (key: keyof CapurroValues, value: string) => {
    setValues((prev) => ({
      ...prev,
      [key]: parseInt(value, 10),
    }));
  };

  const calculateGestationalAge = () => {
    const total = Object.values(values).reduce((sum, value) => sum + (value || 0), 0) + 204;
    const weeks = Math.floor(total / 7);
    const days = total % 7;
    return { total, weeks, days };
  };

  const result = calculateGestationalAge();
  const allFieldsFilled = Object.values(values).every((value) => value !== null);

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col")}>
      <CapurroMetaTags />
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Link to="/calculadoras">
              <Button variant="ghost" size="icon" className="hover:bg-primary/10 dark:hover:bg-primary/20">
                <ChevronLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h1 className={getThemeClasses.gradientHeading("text-3xl")}>
              Capurro Somático
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-300">
            Avaliação da idade gestacional do recém-nascido com base em características clínicas observáveis
          </p>

          <Card className={getThemeClasses.card("p-6 space-y-6")}>
            {(Object.entries(criteria) as [keyof CapurroValues, typeof criteria.mamilo][]).map(([key, options]) => (
              <div key={key} className="space-y-2">
                <Label className="text-base font-medium text-gray-800 dark:text-gray-200">
                  {key.charAt(0).toUpperCase() + key.slice(1).replace('_', ' ')}
                </Label>
                <Select
                  value={values[key]?.toString() || ""}
                  onValueChange={(value) => handleChange(key, value)}
                >
                  <SelectTrigger className={getThemeClasses.select("w-full")}>
                    <SelectValue placeholder="Selecione uma opção" />
                  </SelectTrigger>
                  <SelectContent>
                    {options.map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ))}

            {allFieldsFilled && (
              <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
                <div className="text-center space-y-4">
                  <div className="text-4xl font-bold text-primary dark:text-blue-400">
                    {result.weeks} semanas e {result.days} dias
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Total de pontos: {result.total}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto">
                    Nota: Este método pode apresentar variações em comparação com outros métodos de avaliação da idade gestacional.
                  </div>
                </div>
              </div>
            )}
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default CapurroCalculator;
