
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { toxidromes } from "@/data/toxidromes";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import AppStylePoisoningCard from "@/components/poisonings/AppStylePoisoningCard";

const Poisonings = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-red-50 via-white to-red-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800">
      <HelmetWrapper>
        <title>Intoxicações | PedBook</title>
        <meta name="description" content="Gerenciador de intoxicações do PedBook. Encontre informações sobre toxíndromes, antídotos e doses ajustadas automaticamente com base no peso ou idade do paciente." />
        <meta property="og:title" content="Intoxicações | PedBook" />
        <meta property="og:description" content="Gerenciador de intoxicações do PedBook. Encontre informações sobre toxíndromes, antídotos e doses ajustadas automaticamente com base no peso ou idade do paciente." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://pedb.com.br/poisonings" />
      </HelmetWrapper>

      <Header />

      <main className="flex-1 container mx-auto px-4 py-12">
        <div className="mb-6">
          <Button
            variant="ghost"
            className="gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100"
            onClick={() => navigate('/')}
          >
            <ArrowLeft className="h-4 w-4" />
            Voltar
          </Button>
        </div>

        <div className="text-center space-y-4 mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-red-600 to-red-400 dark:from-red-500 dark:to-red-300">
            Gerenciador de Intoxicações
          </h1>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Encontre rapidamente informações sobre toxíndromes, agentes tóxicos, antídotos e doses ajustadas automaticamente com base no peso ou idade do paciente.
          </p>
        </div>

        <div className="grid grid-cols-2 gap-2 sm:gap-4">
          {toxidromes.map((toxidrome) => (
            <AppStylePoisoningCard
              key={toxidrome.id}
              name={toxidrome.name}
              color={toxidrome.color}
              onClick={() => navigate(`/poisonings/${toxidrome.id}`)}
            />
          ))}
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Poisonings;
