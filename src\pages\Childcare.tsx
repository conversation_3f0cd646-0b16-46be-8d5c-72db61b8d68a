
import { Activity, ChartLine, <PERSON>yringe, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Arrow<PERSON><PERSON><PERSON>, UserRound } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Link } from "react-router-dom";
import { ChildcareMetaTags } from "@/components/childcare/ChildcareMetaTags";
import AppStyleChildcareCard from "@/components/childcare/AppStyleChildcareCard";

const Childcare = () => {

  const categories = [
    {
      title: "Visão Geral do Paciente",
      description: "Análise completa e personalizada do desenvolvimento infantil com recomendações específicas para cada paciente.",
      icon: UserRound,
      color: "bg-blue",
      path: "/puericultura/patient-overview",
      badge: "Mais utilizado",
      isHighlighted: true
    },
    {
      title: "Curvas de Crescimento",
      description: "Veja as principais curvas de crescimento.",
      icon: ChartLine,
      color: "bg-yellow",
      path: "/puericultura/curva-de-crescimento"
    },
    {
      title: "Vacinas",
      description: "Calendário vacinal completo e atualizado.",
      icon: Syringe,
      color: "bg-pink",
      path: "/puericultura/calendario-vacinal"
    },
    {
      title: "Marcos DNPM",
      description: "Veja os os marcos do desenvolvimento neuropsicomotor.",
      icon: Brain,
      color: "bg-blue",
      path: "/dnpm"
    },
    {
      title: "Fórmulas Infantis",
      description: "Guia sobre fórmulas e suplementação infantil.",
      icon: Baby,
      color: "bg-purple",
      path: "/puericultura/formulas"
    },
    {
      title: "Suplementação",
      description: "Encontre orientações automáticas para suplementação de vitaminas conforme as necessidades da criança.",
      icon: PillBottle,
      color: "bg-green",
      path: "/puericultura/suplementacao-infantil",
      badge: "Automático"
    }
  ];


  return (
    <div className="min-h-screen flex flex-col dark:bg-slate-900">
      <ChildcareMetaTags />
      <Header />

      <main className="flex-1 container mx-auto px-4 py-12">
        <Link
          to="/"
          className="hidden sm:inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors mb-8 dark:text-blue-400 dark:hover:text-blue-300"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Voltar para o Menu Inicial</span>
        </Link>

        <div className="text-center space-y-4 mb-12">
          <h1 className="text-3xl md:text-4xl font-bold dark:text-white">
            Puericultura
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto dark:text-gray-300">
            Ferramentas essenciais para acompanhamento do crescimento e desenvolvimento infantil
          </p>
        </div>

        {/* Cards Grid - Todos os cards em um único grid, incluindo o destacado */}
        <div className="grid grid-cols-2 gap-2 sm:gap-4 max-w-7xl mx-auto">
          {categories.map((category, index) => (
            <AppStyleChildcareCard
              key={index}
              {...category}
            />
          ))}
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Childcare;
