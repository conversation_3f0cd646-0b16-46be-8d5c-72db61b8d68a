import React from "react";
import { motion } from "framer-motion";
import { Pill, Users, FileText, Stethoscope } from "lucide-react";

interface StatsSectionProps {
  medicamentosCount?: number;
  usuariosCount?: number;
  prescricoesCount?: number;
  categoriasCount?: number;
}

export const StatsSection = ({
  medicamentosCount = 138,
  usuariosCount = 3560,
  prescricoesCount = 487,
  categoriasCount = 18
}: StatsSectionProps) => {
  
  const stats = [
    {
      icon: Pill,
      value: medicamentosCount,
      label: "Medicamentos",
      suffix: "+",
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      icon: Users,
      value: Math.floor(usuariosCount / 1000),
      label: "Profissionais",
      suffix: "k+",
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      icon: FileText,
      value: prescricoesCount,
      label: "Prescriçõ<PERSON>",
      suffix: "+",
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    },
    {
      icon: Stethoscope,
      value: categoriasCount,
      label: "Categorias",
      suffix: "",
      color: "text-orange-600",
      bgColor: "bg-orange-50"
    }
  ];

  return (
    <div className="py-6 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Título da seção */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-6"
        >
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Confiado por Profissionais
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Dados atualizados em tempo real
          </p>
        </motion.div>

        {/* Grid de estatísticas */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ 
                duration: 0.6, 
                delay: index * 0.1,
                ease: "easeOut"
              }}
              className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-100 dark:border-gray-700 hover:shadow-md transition-shadow duration-300"
            >
              <div className="flex flex-col items-center text-center">
                {/* Ícone */}
                <div className={`${stat.bgColor} p-3 rounded-full mb-3`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
                
                {/* Valor */}
                <div className="mb-1">
                  <span className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">
                    {stat.value.toLocaleString('pt-BR')}
                  </span>
                  <span className={`text-xl font-bold ${stat.color}`}>
                    {stat.suffix}
                  </span>
                </div>
                
                {/* Label */}
                <p className="text-sm text-gray-600 dark:text-gray-300 font-medium">
                  {stat.label}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Texto adicional para SEO */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="text-center mt-6"
        >
          <p className="text-sm text-gray-500 dark:text-gray-400 max-w-2xl mx-auto">
            Plataforma completa de pediatria com cálculos automáticos de doses, 
            prescrições digitais e ferramentas clínicas validadas por especialistas.
          </p>
        </motion.div>
      </div>
    </div>
  );
};
