/**
 * Script de build personalizado com prerendering
 * Gera HTMLs estáticos para cada medicamento e página importante
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { generateRoutes, generateSitemap } from './generateRoutes.js';

console.log('🚀 Iniciando build com prerendering...');

async function buildWithPrerender() {
  try {
    // Passo 1: Gerar rotas dinamicamente
    console.log('\n📋 Passo 1: Gerando rotas...');
    const routes = await generateRoutes();
    
    // Passo 2: Gerar sitemap
    console.log('\n🗺️ Passo 2: Gerando sitemap...');
    await generateSitemap(routes);
    
    // Passo 3: Build normal do Vite
    console.log('\n🔨 Passo 3: Executando build do Vite...');
    execSync('npm run build', { stdio: 'inherit' });
    
    // Passo 4: Prerender páginas específicas
    console.log('\n🎭 Passo 4: Pré-renderizando páginas...');
    await prerenderPages(routes);
    
    // Passo 5: Gerar meta tags específicas
    console.log('\n🏷️ Passo 5: Gerando meta tags...');
    await generateMetaTags();
    
    console.log('\n✅ Build com prerendering concluído!');
    console.log(`📊 Total de páginas geradas: ${routes.length}`);
    
  } catch (error) {
    console.error('\n❌ Erro no build:', error);
    process.exit(1);
  }
}

/**
 * Pré-renderiza páginas específicas usando Puppeteer
 */
async function prerenderPages(routes) {
  const puppeteer = await import('puppeteer');
  
  console.log('🎭 Iniciando prerendering com Puppeteer...');
  
  const browser = await puppeteer.default.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const distPath = path.join(process.cwd(), 'dist');
  
  // Páginas prioritárias para prerender
  const priorityRoutes = routes.filter(route => 
    route.includes('/medicamentos/') || 
    route.includes('/calculadoras/') ||
    route === '/' ||
    route === '/medicamentos/painel'
  ).slice(0, 50); // Limitar a 50 páginas para não demorar muito
  
  console.log(`🎯 Pré-renderizando ${priorityRoutes.length} páginas prioritárias...`);
  
  for (const route of priorityRoutes) {
    try {
      const page = await browser.newPage();
      
      // Configurar página
      await page.setViewport({ width: 1200, height: 800 });
      await page.setUserAgent('Mozilla/5.0 (compatible; PedBookBot/1.0; +https://pedb.com.br)');
      
      // Navegar para a página
      const url = `http://localhost:8080${route}`;
      await page.goto(url, { 
        waitUntil: 'networkidle0',
        timeout: 30000 
      });
      
      // Aguardar renderização
      await page.waitForTimeout(2000);
      
      // Obter HTML renderizado
      const html = await page.content();
      
      // Salvar HTML
      const filePath = route === '/' ? 
        path.join(distPath, 'index.html') :
        path.join(distPath, route.slice(1), 'index.html');
      
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      fs.writeFileSync(filePath, html);
      console.log(`✅ Gerado: ${route}`);
      
      await page.close();
      
    } catch (error) {
      console.warn(`⚠️ Erro ao renderizar ${route}:`, error.message);
    }
  }
  
  await browser.close();
  console.log('🎉 Prerendering concluído!');
}

/**
 * Gera meta tags específicas para medicamentos
 */
async function generateMetaTags() {
  console.log('🏷️ Gerando meta tags específicas...');
  
  // Carregar dados dos medicamentos
  const routesData = JSON.parse(
    fs.readFileSync(path.join(process.cwd(), 'prerender-routes.json'), 'utf8')
  );
  
  const distPath = path.join(process.cwd(), 'dist');
  
  // Processar cada medicamento
  routesData.medications.forEach(med => {
    if (!med.slug) return;
    
    const htmlPath = path.join(distPath, 'medicamentos', med.slug, 'index.html');
    
    if (fs.existsSync(htmlPath)) {
      let html = fs.readFileSync(htmlPath, 'utf8');
      
      // Substituir meta tags genéricas por específicas
      const specificTitle = `${med.name} - Dose Pediátrica | PedBook`;
      const specificDescription = `Dose pediátrica do ${med.name}: calculadora automática, indicações, contraindicações e posologia completa para pediatria. ${med.description || ''}`.substring(0, 160);
      const specificKeywords = `${med.name.toLowerCase()}, dose pediátrica, posologia infantil, calculadora dose, pediatria, ${med.category || ''}`;
      
      // Substituir title
      html = html.replace(
        /<title>.*?<\/title>/,
        `<title>${specificTitle}</title>`
      );
      
      // Substituir description
      html = html.replace(
        /<meta name="description" content=".*?">/,
        `<meta name="description" content="${specificDescription}">`
      );
      
      // Substituir keywords
      html = html.replace(
        /<meta name="keywords" content=".*?">/,
        `<meta name="keywords" content="${specificKeywords}">`
      );
      
      // Substituir Open Graph
      html = html.replace(
        /<meta property="og:title" content=".*?">/,
        `<meta property="og:title" content="${specificTitle}">`
      );
      
      html = html.replace(
        /<meta property="og:description" content=".*?">/,
        `<meta property="og:description" content="${specificDescription}">`
      );
      
      html = html.replace(
        /<meta property="og:url" content=".*?">/,
        `<meta property="og:url" content="https://pedb.com.br/medicamentos/${med.slug}">`
      );
      
      // Adicionar JSON-LD específico para medicamento
      const jsonLd = {
        "@context": "https://schema.org",
        "@type": "Drug",
        "name": med.name,
        "description": specificDescription,
        "url": `https://pedb.com.br/medicamentos/${med.slug}`,
        "manufacturer": "Diversos laboratórios",
        "activeIngredient": med.name,
        "dosageForm": "Suspensão oral, Comprimido",
        "specialty": "Pediatria",
        "audience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde"
        }
      };
      
      // Inserir JSON-LD antes do </head>
      html = html.replace(
        '</head>',
        `  <script type="application/ld+json">${JSON.stringify(jsonLd, null, 2)}</script>\n</head>`
      );
      
      fs.writeFileSync(htmlPath, html);
      console.log(`✅ Meta tags atualizadas: ${med.name}`);
    }
  });
  
  console.log('🎉 Meta tags geradas!');
}

// Executar build
buildWithPrerender();
