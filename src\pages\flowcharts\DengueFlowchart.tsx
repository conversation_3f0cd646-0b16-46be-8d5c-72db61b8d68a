
import React, { useState } from "react";
import { ArrowLeft } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { DenguePatientForm } from "@/components/flowcharts/dengue/DenguePatientForm";
import { DengueQuestion } from "@/components/flowcharts/dengue/DengueQuestion";
import { DengueResult } from "@/components/flowcharts/dengue/DengueResult";
import { useDengueFlow } from "@/components/flowcharts/dengue/useDengueFlow";
import { getThemeClasses } from "@/components/ui/theme-utils";

const DengueFlowchart = () => {
  const [weight, setWeight] = useState<string>("");
  const [age, setAge] = useState<string>("");
  const [showForm, setShowForm] = useState(true);
  
  const flow = useDengueFlow(parseFloat(weight));

  const handleStartFlow = (e: React.FormEvent) => {
    e.preventDefault();
    if (weight && age) {
      setShowForm(false);
    }
  };

  const handleContinue = (nextStep: string) => {
    flow.setCurrentStep(nextStep);
  };

  return (
    <div className={getThemeClasses.pageBackground("min-h-screen flex flex-col from-orange-50 via-white to-orange-50 dark:from-orange-950 dark:via-slate-900 dark:to-slate-800")}>
      <HelmetWrapper>
        <title>PedBook | Dengue</title>
        <meta name="description" content="Fluxograma interativo para manejo de casos suspeitos de dengue em pediatria" />
      </HelmetWrapper>

      <Header />
      <main className="flex-1 container mx-auto px-4 py-8">
        <Link 
          to="/flowcharts" 
          className="inline-flex items-center gap-2 text-orange-600 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 transition-colors mb-8"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Voltar para Fluxogramas</span>
        </Link>

        <div className="max-w-4xl mx-auto space-y-8">
          <div className="text-center space-y-4">
            <span className="text-4xl">🦟</span>
            <h1 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-orange-600 to-red-600 dark:from-orange-400 dark:to-red-400">
              Fluxograma de Dengue
            </h1>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Guia interativo para manejo de casos suspeitos de dengue em pediatria
            </p>
          </div>

          {showForm && (
            <div className="bg-red-50 border-2 border-red-200 rounded-lg p-6 space-y-4 mb-8 dark:bg-red-900/20 dark:border-red-700/50">
              <h2 className="text-xl font-bold text-red-700 dark:text-red-300 text-center uppercase">
                Suspeita de Dengue
              </h2>
              <p className="text-gray-700 dark:text-gray-200 leading-relaxed">
                Relato de febre, usualmente entre dois e sete dias de duração, e duas ou mais das seguintes manifestações: náusea, vômitos; exantema; mialgia, artralgia;
                cefaleia, dor retro-orbital; petéquias; prova do laço positiva e leucopenia. Também pode ser considerado caso suspeito toda criança com quadro febril agudo,
                usualmente entre dois e sete dias de duração, e sem foco de infecção aparente.
              </p>
              <p className="text-red-600 dark:text-red-300 font-semibold text-center mt-4">
                NOTIFICAR TODO CASO SUSPEITO DE DENGUE
              </p>
            </div>
          )}

          {showForm ? (
            <DenguePatientForm
              weight={weight}
              age={age}
              onWeightChange={setWeight}
              onAgeChange={setAge}
              onSubmit={handleStartFlow}
            />
          ) : (
            <>
              {flow.getQuestion() && (
                <DengueQuestion
                  question={flow.getQuestion()}
                  onAnswer={flow.handleAnswer}
                  selectedAnswer={flow.answers[flow.currentStep]}
                />
              )}
              {flow.getResult() && (
                <DengueResult
                  {...flow.getResult()!}
                  onReset={flow.resetFlow}
                  onContinue={handleContinue}
                  weight={parseFloat(weight)}
                  age={parseFloat(age)}
                />
              )}
            </>
          )}
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default DengueFlowchart;
