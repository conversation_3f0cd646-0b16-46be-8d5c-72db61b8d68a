
import React from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { ArrowLeft } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { TreatmentFlow } from "@/components/flowcharts/asthma/TreatmentFlow";
import { getThemeClasses } from "@/components/ui/theme-utils";

const AsthmaFlowchart = () => {
  const [selectedAgeGroup, setSelectedAgeGroup] = React.useState<"under6" | "over6" | null>(null);
  const [selectedFlow, setSelectedFlow] = React.useState<"mild" | "severe" | null>(null);
  const [showImprovement, setShowImprovement] = React.useState(false);
  const [improved, setImproved] = React.useState<boolean | null>(null);
  const [showAssessment, setShowAssessment] = React.useState(false);

  const handleSeveritySelection = (severity: "mild" | "severe") => {
    setSelectedFlow(severity);
    setShowImprovement(severity === "mild");
    setImproved(null);
  };

  const handleImprovement = (hasImproved: boolean) => {
    setImproved(hasImproved);
  };

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col from-blue-100 via-white to-purple-50 dark:from-blue-950 dark:via-slate-900 dark:to-purple-950")}>
      <HelmetWrapper>
        <title>PedBook | Asma</title>
        <meta name="description" content="Fluxograma interativo para manejo de crise asmática em pediatria" />
      </HelmetWrapper>

      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <Link 
          to="/flowcharts" 
          className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors mb-8"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Voltar para Fluxogramas</span>
        </Link>

        <div className="max-w-4xl mx-auto space-y-8">
          <div className="text-center space-y-4">
            <h1 className={getThemeClasses.gradientHeading("text-3xl font-bold from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400")}>
              Manejo da Exacerbação da Asma em Crianças
            </h1>
            
            <Card className={getThemeClasses.gradientCard("blue", "p-6")}>
              <h2 className="text-xl font-semibold text-blue-800 dark:text-blue-300 mb-4">Quadro Clínico - Crise de Asma</h2>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                Parte de episódios de descompensação do quadro de base (asma), observe os sintomas:
              </p>
              <ul className="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-200">
                <li>Dispneia</li>
                <li>Tosse</li>
                <li>Sibilos na ausculta</li>
                <li>Aperto no peito ou dor torácica</li>
                <li>Redução progressiva na função pulmonar: Redução da volume expiratório forçado no primeiro segundo (VEF1) ou Pico de fluxo expiratório (PEF)</li>
                <li>Uso de musculatura acessória</li>
                <li>Hipoxemia</li>
                <li>Sonolência, confusão ou alteração do estado mental</li>
                <li>Silêncio respiratório (indica obstrução crítica das vias aéreas)</li>
              </ul>
            </Card>

            {!showAssessment ? (
              <Button 
                size="lg"
                onClick={() => setShowAssessment(true)}
                className="mt-6 bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-700 dark:hover:bg-blue-800"
              >
                Iniciar Avaliação
              </Button>
            ) : (
              <Card className={getThemeClasses.gradientCard("purple", "p-6 mt-8")}>
                <h3 className="text-xl font-semibold text-purple-800 dark:text-purple-300 mb-4">
                  Selecione a Faixa Etária
                </h3>
                <div className="flex gap-4 justify-center">
                  <Button
                    onClick={() => setSelectedAgeGroup("under6")}
                    variant={selectedAgeGroup === "under6" ? "default" : "outline"}
                    className="w-48"
                  >
                    Menor de 6 anos
                  </Button>
                  <Button
                    onClick={() => setSelectedAgeGroup("over6")}
                    variant={selectedAgeGroup === "over6" ? "default" : "outline"}
                    className="w-48"
                  >
                    Maior de 6 anos
                  </Button>
                </div>
              </Card>
            )}

            {selectedAgeGroup && showAssessment && (
              <Card className={getThemeClasses.gradientCard("purple", "p-6 mt-8")}>
                <h3 className="text-xl font-semibold text-purple-800 dark:text-purple-300 mb-4">
                  Selecione a Gravidade da Asma
                </h3>
                <div className="flex gap-4 justify-center">
                  <Button
                    onClick={() => handleSeveritySelection("mild")}
                    variant={selectedFlow === "mild" ? "default" : "outline"}
                    className="w-48"
                  >
                    Leve ou Moderada
                  </Button>
                  <Button
                    onClick={() => handleSeveritySelection("severe")}
                    variant={selectedFlow === "severe" ? "destructive" : "outline"}
                    className="w-48"
                  >
                    Severa
                  </Button>
                </div>
              </Card>
            )}

            {selectedFlow && selectedAgeGroup && (
              <TreatmentFlow
                ageGroup={selectedAgeGroup}
                weight={0}
                selectedFlow={selectedFlow}
                improved={improved}
                onSeveritySelect={handleSeveritySelection}
                onImprovement={handleImprovement}
              />
            )}
          </div>
        </div>
        <div className="w-full text-center p-4 bg-white dark:bg-slate-800 mt-8 rounded-lg border border-gray-100 dark:border-gray-700">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Fonte: RIBEIRÃO PRETO. Secretaria da Saúde de Ribeirão Preto. Protocolo de Manejo de Crise Asmática em Crianças e Adolescentes. 2023
          </p>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default AsthmaFlowchart;
