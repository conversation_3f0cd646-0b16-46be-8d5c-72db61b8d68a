import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import "./ICDSearchForm.css"

interface ICDSearchFormProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  setIsSearching: (isSearching: boolean) => void;
  handleSearch: (e: React.FormEvent) => void;
}

export const ICDSearchForm = ({
  searchTerm,
  setSearchTerm,
  setIsSearching,
  handleSearch,
}: ICDSearchFormProps) => {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    if (value.length >= 2) {
      setIsSearching(true);
    } else {
      setIsSearching(false);
    }
  };

  return (
    <form onSubmit={handleSearch} className="relative max-w-2xl mx-auto">
      <div className="relative group">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-primary/5 to-transparent rounded-lg blur-lg group-hover:opacity-75 transition-opacity" />
        <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-primary/60 group-hover:text-primary/80 transition-colors" />
        <Input
          type="text"
          value={searchTerm}
          onChange={handleInputChange}
          placeholder="Digite o nome da doença ou código CID..."
          className="pl-7 pr-4 py-6 text-lg bg-white/80 backdrop-blur-sm border-primary/20 focus:border-primary/40 rounded-lg shadow-lg shadow-primary/5 input-search"
        />
      </div>
    </form>
  );
};