import React from "react";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import { Pill, TrendingUp } from "lucide-react";
import { Button } from "@/components/ui/button";

interface PopularMedication {
  name: string;
  slug: string;
  category: string;
}

interface PopularMedicationsProps {
  medications?: PopularMedication[];
}

export const PopularMedications = ({ 
  medications = [] 
}: PopularMedicationsProps) => {
  const navigate = useNavigate();

  // Mostrar apenas os primeiros 8 medicamentos
  const displayMedications = medications.slice(0, 8);

  if (displayMedications.length === 0) {
    return null;
  }

  return (
    <div className="py-6 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Título da seção */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-6"
        >
          <div className="flex items-center justify-center gap-2 mb-2">
            <TrendingUp className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Medicamentos Populares
            </h2>
          </div>
          <p className="text-gray-600 dark:text-gray-300">
            Acesso rápido aos medicamentos mais consultados
          </p>
        </motion.div>

        {/* Grid de medicamentos */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {displayMedications.map((medication, index) => (
            <motion.div
              key={medication.slug}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ 
                duration: 0.6, 
                delay: index * 0.05,
                ease: "easeOut"
              }}
            >
              <Button
                variant="outline"
                className="w-full h-auto p-3 flex flex-col items-start text-left hover:bg-primary/5 hover:border-primary/30 transition-all duration-300 group"
                onClick={() => navigate(`/medicamentos/${medication.slug}`)}
              >
                <div className="flex items-center gap-2 mb-2 w-full">
                  <Pill className="h-4 w-4 text-primary group-hover:text-primary/80" />
                  <span className="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {medication.category}
                  </span>
                </div>
                
                <h3 className="font-semibold text-sm text-gray-900 dark:text-white group-hover:text-primary transition-colors duration-300 line-clamp-2">
                  {medication.name}
                </h3>
              </Button>
            </motion.div>
          ))}
        </div>

        {/* Link para ver todos */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-center mt-6"
        >
          <Button
            variant="ghost"
            onClick={() => navigate('/medicamentos/painel')}
            className="text-primary hover:text-primary/80 hover:bg-primary/5"
          >
            Ver todos os medicamentos →
          </Button>
        </motion.div>

        {/* Texto adicional para SEO */}
        <div className="hidden">
          <h3>Calculadora de Doses Pediátricas</h3>
          <p>
            Acesse nossa calculadora completa com {medications.length}+ medicamentos pediátricos. 
            Calcule doses automaticamente baseadas no peso e idade da criança. 
            Inclui medicamentos como {displayMedications.slice(0, 5).map(m => m.name).join(', ')} 
            e muitos outros essenciais para a prática pediátrica.
          </p>
          
          <h4>Categorias de Medicamentos Disponíveis</h4>
          <p>
            {Array.from(new Set(displayMedications.map(m => m.category))).join(', ')} 
            e outras categorias especializadas para pediatria.
          </p>
        </div>
      </div>
    </div>
  );
};
