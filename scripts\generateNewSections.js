/**
 * Script para gerar páginas SEO das NOVAS seções implementadas
 * (Calculadoras, Puericultura, Intoxicações, Fluxogramas, CID-10)
 */

import fs from 'fs';
import path from 'path';

console.log('🚀 Gerando páginas SEO das NOVAS seções...');

/**
 * Dados das calculadoras
 */
const calculatorsData = [
  {
    slug: 'imc',
    name: 'Calculadora de IMC Pediátrico',
    description: 'Calcule o Índice de Massa Corporal (IMC) para crianças e adolescentes com percentis específicos para idade.',
    keywords: 'imc pediatrico, indice massa corporal crianca, percentil imc, obesidade infantil, desnutricao pediatrica'
  },
  {
    slug: 'hidratacao',
    name: 'Calculadora de Hidratação',
    description: 'Calcule necessidades hídricas e reposição de fluidos em pediatria com base no peso e grau de desidratação.',
    keywords: 'hidratacao pediatrica, reposicao fluidos, desidratacao crianca, soro fisiologico, eletroliticos'
  },
  {
    slug: 'superficie-corporal',
    name: '<PERSON>culador<PERSON> de Superfície Corporal',
    description: 'Calcule a área de superfície corporal (ASC) pediátrica usando fórmulas validadas para dosagem de medicamentos.',
    keywords: 'superficie corporal pediatrica, asc crianca, area superficie corporal, dosagem medicamento'
  },
  {
    slug: 'apgar',
    name: 'Calculadora de Apgar',
    description: 'Avalie o estado do recém-nascido através do escore de Apgar nos primeiros minutos de vida.',
    keywords: 'apgar score, escore apgar, avaliacao recem nascido, vitalidade neonatal'
  },
  {
    slug: 'glasgow',
    name: 'Escala de Glasgow Pediátrica',
    description: 'Avalie o nível de consciência em crianças usando a Escala de Coma de Glasgow adaptada para pediatria.',
    keywords: 'glasgow pediatrico, escala coma glasgow, nivel consciencia crianca, trauma craniano'
  },
  {
    slug: 'capurro',
    name: 'Método de Capurro',
    description: 'Determine a idade gestacional do recém-nascido através do método de Capurro com avaliação somática.',
    keywords: 'capurro metodo, idade gestacional, avaliacao somatica, recem nascido prematuro'
  },
  {
    slug: 'ballard',
    name: 'Método de Ballard',
    description: 'Avalie a maturidade do recém-nascido através do método de Ballard com critérios neurológicos e físicos.',
    keywords: 'ballard score, maturidade neonatal, idade gestacional ballard, avaliacao neurologica'
  },
  {
    slug: 'finnegan',
    name: 'Escala de Finnegan',
    description: 'Avalie síndrome de abstinência neonatal através da escala de Finnegan para recém-nascidos.',
    keywords: 'finnegan escala, sindrome abstinencia neonatal, abstinencia drogas recem nascido'
  },
  {
    slug: 'bhutani',
    name: 'Nomograma de Bhutani',
    description: 'Avalie o risco de hiperbilirrubinemia em recém-nascidos através do nomograma de Bhutani.',
    keywords: 'bhutani nomograma, hiperbilirrubinemia, ictericia neonatal, bilirrubina recem nascido'
  },
  {
    slug: 'rodwell',
    name: 'Calculadora de Rodwell',
    description: 'Avalie parâmetros hematológicos em recém-nascidos através dos valores de referência de Rodwell.',
    keywords: 'rodwell valores, hematologia neonatal, hemograma recem nascido, valores referencia'
  },
  {
    slug: 'gina',
    name: 'Calculadora GINA',
    description: 'Avalie e classifique a asma pediátrica seguindo as diretrizes GINA (Global Initiative for Asthma).',
    keywords: 'gina asma, classificacao asma pediatrica, controle asma crianca, diretrizes gina'
  }
];

/**
 * Dados da puericultura
 */
const puericultureData = [
  {
    slug: 'puericultura',
    name: 'Puericultura',
    description: 'Acompanhamento completo do desenvolvimento infantil com ferramentas para consultas de puericultura.',
    keywords: 'puericultura, acompanhamento infantil, desenvolvimento crianca, consulta pediatrica'
  },
  {
    slug: 'puericultura/curva-de-crescimento',
    name: 'Curvas de Crescimento',
    description: 'Monitore o crescimento infantil com curvas de peso, altura e perímetro cefálico da OMS.',
    keywords: 'curva crescimento, percentil peso altura, oms crescimento, desenvolvimento fisico'
  },
  {
    slug: 'puericultura/calendario-vacinal',
    name: 'Calendário Vacinal',
    description: 'Calendário de vacinação infantil atualizado com todas as vacinas recomendadas pelo Ministério da Saúde.',
    keywords: 'calendario vacinal, vacinas crianca, imunizacao pediatrica, esquema vacinal'
  },
  {
    slug: 'puericultura/formulas',
    name: 'Fórmulas Infantis',
    description: 'Guia completo sobre fórmulas infantis, preparo e indicações para alimentação de lactentes.',
    keywords: 'formula infantil, leite artificial, alimentacao lactente, preparo mamadeira'
  },
  {
    slug: 'puericultura/suplementacao-infantil',
    name: 'Suplementação Infantil',
    description: 'Orientações sobre suplementação vitamínica e mineral em crianças conforme faixa etária.',
    keywords: 'suplementacao infantil, vitaminas crianca, ferro pediatrico, vitamina d'
  },
  {
    slug: 'puericultura/patient-overview',
    name: 'Visão Geral do Paciente',
    description: 'Ferramenta para avaliação global do paciente pediátrico com dados antropométricos e desenvolvimento.',
    keywords: 'avaliacao pediatrica, dados antropometricos, desenvolvimento neuropsicomotor'
  },
  {
    slug: 'dnpm',
    name: 'DNPM - Desenvolvimento Neuropsicomotor',
    description: 'Avalie marcos do desenvolvimento neuropsicomotor em crianças por faixa etária.',
    keywords: 'dnpm, desenvolvimento neuropsicomotor, marcos desenvolvimento, atraso desenvolvimento'
  }
];

/**
 * Dados das intoxicações
 */
const poisoningsData = [
  {
    slug: 'poisonings',
    name: 'Intoxicações Pediátricas',
    description: 'Guia completo para manejo de intoxicações em pediatria com antídotos e doses específicas.',
    keywords: 'intoxicacao pediatrica, envenenamento crianca, antidotos pediatricos, emergencia toxicologica'
  },
  {
    slug: 'poisonings/benzodiazepinicos',
    name: 'Intoxicação por Benzodiazepínicos',
    description: 'Manejo da intoxicação por benzodiazepínicos em crianças: sintomas, antídoto flumazenil e doses.',
    keywords: 'benzodiazepinicos intoxicacao, flumazenil pediatrico, overdose benzodiazepinicos'
  },
  {
    slug: 'poisonings/opioides',
    name: 'Intoxicação por Opioides',
    description: 'Tratamento da intoxicação por opioides em pediatria: naloxona, doses e manejo da depressão respiratória.',
    keywords: 'opioides intoxicacao, naloxona pediatrica, overdose opioides, depressao respiratoria'
  },
  {
    slug: 'poisonings/anticolinergicos',
    name: 'Intoxicação por Anticolinérgicos',
    description: 'Manejo da intoxicação por anticolinérgicos em crianças: sintomas, fisostigmina e cuidados específicos.',
    keywords: 'anticolinergicos intoxicacao, fisostigmina pediatrica, atropina overdose'
  },
  {
    slug: 'poisonings/simpatomimeticos',
    name: 'Intoxicação por Simpatomiméticos',
    description: 'Tratamento da intoxicação por simpatomiméticos: anfetaminas, cocaína e manejo cardiovascular.',
    keywords: 'simpatomimeticos intoxicacao, anfetaminas pediatrica, cocaina crianca'
  },
  {
    slug: 'poisonings/colinergicos',
    name: 'Intoxicação por Colinérgicos',
    description: 'Manejo da intoxicação por organofosforados e carbamatos: atropina, pralidoxima e descontaminação.',
    keywords: 'colinergicos intoxicacao, organofosforados, atropina pralidoxima'
  },
  {
    slug: 'poisonings/metemoglobinemia',
    name: 'Metemoglobinemia',
    description: 'Diagnóstico e tratamento da metemoglobinemia em pediatria: azul de metileno e causas.',
    keywords: 'metemoglobinemia pediatrica, azul metileno, cianose central'
  },
  {
    slug: 'poisonings/paracetamol',
    name: 'Intoxicação por Paracetamol',
    description: 'Manejo da intoxicação por paracetamol: N-acetilcisteína, nomograma e hepatotoxicidade.',
    keywords: 'paracetamol intoxicacao, acetaminofeno overdose, n-acetilcisteina'
  },
  {
    slug: 'poisonings/antidepressivos_triciclicos',
    name: 'Intoxicação por Antidepressivos Tricíclicos',
    description: 'Tratamento da intoxicação por tricíclicos: bicarbonato de sódio, arritmias e convulsões.',
    keywords: 'triciclicos intoxicacao, bicarbonato sodio, arritmias pediatricas'
  },
  {
    slug: 'poisonings/betabloqueadores',
    name: 'Intoxicação por Betabloqueadores',
    description: 'Manejo da intoxicação por betabloqueadores: glucagon, atropina e suporte cardiovascular.',
    keywords: 'betabloqueadores intoxicacao, glucagon pediatrico, bradicardia'
  }
];

/**
 * Dados dos fluxogramas
 */
const flowchartsData = [
  {
    slug: 'fluxogramas/asma',
    name: 'Fluxograma de Asma',
    description: 'Fluxograma para diagnóstico e manejo da asma pediátrica com classificação de gravidade.',
    keywords: 'asma pediatrica, fluxograma asma, broncoespasmo crianca, tratamento asma'
  },
  {
    slug: 'fluxogramas/anafilaxia',
    name: 'Fluxograma de Anafilaxia',
    description: 'Protocolo de emergência para anafilaxia pediátrica: reconhecimento e tratamento imediato.',
    keywords: 'anafilaxia pediatrica, choque anafilatico, epinefrina crianca, alergia grave'
  },
  {
    slug: 'fluxogramas/convulsao',
    name: 'Fluxograma de Convulsão',
    description: 'Manejo da convulsão pediátrica: status epilepticus, medicações e protocolo de emergência.',
    keywords: 'convulsao pediatrica, status epilepticus, anticonvulsivantes, emergencia neurologica'
  },
  {
    slug: 'fluxogramas/cetoacidose',
    name: 'Fluxograma de Cetoacidose',
    description: 'Protocolo para cetoacidose diabética pediátrica: hidratação, insulina e correção eletrolítica.',
    keywords: 'cetoacidose diabetica, diabetes pediatrico, insulina crianca, acidose metabolica'
  },
  {
    slug: 'fluxogramas/dengue',
    name: 'Fluxograma de Dengue',
    description: 'Manejo da dengue pediátrica: classificação, sinais de alarme e tratamento.',
    keywords: 'dengue pediatrica, febre hemorragica, choque dengue, sinais alarme'
  },
  {
    slug: 'fluxogramas/pecarn',
    name: 'Fluxograma PECARN',
    description: 'Regra PECARN para trauma craniano pediátrico: indicações para tomografia computadorizada.',
    keywords: 'pecarn regra, trauma craniano pediatrico, tomografia crianca, traumatismo cranio'
  },
  {
    slug: 'fluxogramas/animais-peconhentos/botrópico',
    name: 'Acidente Botrópico',
    description: 'Manejo do acidente botrópico em crianças: soro antibotrópico, doses e complicações.',
    keywords: 'acidente botropico, jararaca picada, soro antibotropico, envenenamento ofidico'
  },
  {
    slug: 'fluxogramas/animais-peconhentos/crotálico',
    name: 'Acidente Crotálico',
    description: 'Tratamento do acidente crotálico pediátrico: soro anticrotálico e manifestações clínicas.',
    keywords: 'acidente crotalico, cascavel picada, soro anticrotalico, miotoxicidade'
  },
  {
    slug: 'fluxogramas/animais-peconhentos/elapídico',
    name: 'Acidente Elapídico',
    description: 'Manejo do acidente elapídico: coral verdadeira, soro antielapídico e paralisia.',
    keywords: 'acidente elapidico, coral picada, soro antielapidico, paralisia flacida'
  },
  {
    slug: 'fluxogramas/animais-peconhentos/loxoscélico',
    name: 'Acidente Loxoscélico',
    description: 'Tratamento do loxoscelismo: aranha marrom, necrose cutânea e soro antiloxoscélico.',
    keywords: 'loxoscelismo, aranha marrom, necrose cutanea, soro antiloxoscelico'
  },
  {
    slug: 'fluxogramas/animais-peconhentos/phoneutria',
    name: 'Acidente por Phoneutria',
    description: 'Manejo do acidente por Phoneutria: aranha armadeira, dor local e soro antiaracnídico.',
    keywords: 'phoneutria picada, aranha armadeira, soro antiaracnidico, dor neuropatica'
  },
  {
    slug: 'fluxogramas/animais-peconhentos/escorpiao',
    name: 'Acidente Escorpiônico',
    description: 'Tratamento do acidente escorpiônico pediátrico: soro antiescorpiônico e manifestações.',
    keywords: 'acidente escorpionico, escorpiao picada, soro antiescorpionico, dor local'
  }
];

/**
 * Dados do CID-10
 */
const icdData = [
  {
    slug: 'icd',
    name: 'CID-10 Pediátrico',
    description: 'Classificação Internacional de Doenças (CID-10) com códigos específicos para pediatria.',
    keywords: 'cid 10 pediatrico, classificacao doencas, codigos cid pediatria, diagnosticos pediatricos'
  }
];

/**
 * Gerar páginas HTML para calculadoras
 */
async function generateCalculatorPages() {
  console.log('\n🧮 Gerando páginas de calculadoras...');
  
  const distPath = path.join(process.cwd(), 'dist');
  const indexPath = path.join(distPath, 'index.html');
  
  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html não encontrado');
    return 0;
  }
  
  const baseHtml = fs.readFileSync(indexPath, 'utf8');
  let successCount = 0;
  
  for (const calc of calculatorsData) {
    try {
      const calcDir = path.join(distPath, 'calculadoras', calc.slug);
      if (!fs.existsSync(calcDir)) {
        fs.mkdirSync(calcDir, { recursive: true });
      }
      
      const title = `${calc.name} | PedBook`;
      const description = calc.description;
      const keywords = calc.keywords;
      
      let customHtml = baseHtml;
      
      // Substituir meta tags
      customHtml = customHtml.replace(/<title>.*?<\/title>/, `<title>${title}</title>`);
      customHtml = customHtml.replace(/<meta name="description" content=".*?" ?\/>/, `<meta name="description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta name="keywords" content=".*?" ?\/>/, `<meta name="keywords" content="${keywords}" />`);
      
      // URL canônica
      const calcUrl = `https://pedb.com.br/calculadoras/${calc.slug}`;
      customHtml = customHtml.replace(/<link rel="canonical" href=".*?" \/>/, `<link rel="canonical" href="${calcUrl}" />`);
      
      // Open Graph
      customHtml = customHtml.replace(/<meta property="og:title" content=".*?" ?\/>/, `<meta property="og:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta property="og:description" content=".*?" ?\/>/, `<meta property="og:description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta property="og:url" content=".*?" ?\/>/, `<meta property="og:url" content="${calcUrl}" />`);
      
      // Twitter Cards
      customHtml = customHtml.replace(/<meta name="twitter:title" content=".*?" ?\/>/, `<meta name="twitter:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta name="twitter:description" content=".*?" ?\/>/, `<meta name="twitter:description" content="${description}" />`);
      
      // Conteúdo SEO
      const seoContent = `
    <div id="root"></div>
    <!-- SEO Content for Crawlers -->
    <div style="display: none;" data-seo-content>
      <h1>${calc.name}</h1>
      <p>${calc.description}</p>
      <h2>Calculadora Pediátrica Automática</h2>
      <p>Ferramenta especializada para cálculos em pediatria com resultados precisos e baseados em evidências científicas.</p>
      <h2>Como Usar</h2>
      <p>Insira os dados solicitados e obtenha resultados automáticos com interpretação clínica adequada para pediatria.</p>
    </div>`;
      
      customHtml = customHtml.replace('<div id="root"></div>', seoContent);
      
      // Schema.org
      const schema = {
        "@context": "https://schema.org",
        "@type": "MedicalWebPage",
        "name": calc.name,
        "description": description,
        "url": calcUrl,
        "medicalAudience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde Pediátrica"
        },
        "about": {
          "@type": "MedicalProcedure",
          "name": calc.name,
          "medicalSpecialty": "Pediatria"
        },
        "publisher": {
          "@type": "Organization",
          "name": "PedBook",
          "url": "https://pedb.com.br"
        }
      };
      
      const jsonLd = `<script type="application/ld+json">${JSON.stringify(schema, null, 2)}</script>`;
      customHtml = customHtml.replace('</head>', `  ${jsonLd}\n</head>`);
      
      fs.writeFileSync(path.join(calcDir, 'index.html'), customHtml);
      console.log(`✅ Criado: /calculadoras/${calc.slug}/index.html`);
      successCount++;
      
    } catch (err) {
      console.error(`❌ Erro ao gerar ${calc.slug}:`, err.message);
    }
  }
  
  console.log(`🧮 Calculadoras: ${successCount}/${calculatorsData.length} páginas geradas`);
  return successCount;
}

/**
 * Gerar páginas HTML para puericultura
 */
async function generatePuericulturePages() {
  console.log('\n👶 Gerando páginas de puericultura...');

  const distPath = path.join(process.cwd(), 'dist');
  const indexPath = path.join(distPath, 'index.html');

  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html não encontrado');
    return 0;
  }

  const baseHtml = fs.readFileSync(indexPath, 'utf8');
  let successCount = 0;

  for (const page of puericultureData) {
    try {
      const pageDir = path.join(distPath, page.slug);
      if (!fs.existsSync(pageDir)) {
        fs.mkdirSync(pageDir, { recursive: true });
      }

      const title = `${page.name} - Puericultura | PedBook`;
      const description = page.description;
      const keywords = page.keywords;

      let customHtml = baseHtml;

      // Substituir meta tags
      customHtml = customHtml.replace(/<title>.*?<\/title>/, `<title>${title}</title>`);
      customHtml = customHtml.replace(/<meta name="description" content=".*?" ?\/>/, `<meta name="description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta name="keywords" content=".*?" ?\/>/, `<meta name="keywords" content="${keywords}" />`);

      // URL canônica
      const pageUrl = `https://pedb.com.br/${page.slug}`;
      customHtml = customHtml.replace(/<link rel="canonical" href=".*?" \/>/, `<link rel="canonical" href="${pageUrl}" />`);

      // Open Graph
      customHtml = customHtml.replace(/<meta property="og:title" content=".*?" ?\/>/, `<meta property="og:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta property="og:description" content=".*?" ?\/>/, `<meta property="og:description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta property="og:url" content=".*?" ?\/>/, `<meta property="og:url" content="${pageUrl}" />`);

      // Twitter Cards
      customHtml = customHtml.replace(/<meta name="twitter:title" content=".*?" ?\/>/, `<meta name="twitter:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta name="twitter:description" content=".*?" ?\/>/, `<meta name="twitter:description" content="${description}" />`);

      // Conteúdo SEO
      const seoContent = `
    <div id="root"></div>
    <!-- SEO Content for Crawlers -->
    <div style="display: none;" data-seo-content>
      <h1>${page.name}</h1>
      <p>${page.description}</p>
      <h2>Acompanhamento Pediátrico</h2>
      <p>Ferramenta especializada para acompanhamento do desenvolvimento infantil e consultas de puericultura.</p>
      <h2>Desenvolvimento Infantil</h2>
      <p>Monitore marcos do desenvolvimento, crescimento e saúde da criança de forma sistemática.</p>
    </div>`;

      customHtml = customHtml.replace('<div id="root"></div>', seoContent);

      // Schema.org
      const schema = {
        "@context": "https://schema.org",
        "@type": "MedicalWebPage",
        "name": page.name,
        "description": description,
        "url": pageUrl,
        "medicalAudience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde Pediátrica"
        },
        "about": {
          "@type": "MedicalCondition",
          "name": page.name,
          "medicalSpecialty": "Pediatria"
        },
        "publisher": {
          "@type": "Organization",
          "name": "PedBook",
          "url": "https://pedb.com.br"
        }
      };

      const jsonLd = `<script type="application/ld+json">${JSON.stringify(schema, null, 2)}</script>`;
      customHtml = customHtml.replace('</head>', `  ${jsonLd}\n</head>`);

      fs.writeFileSync(path.join(pageDir, 'index.html'), customHtml);
      console.log(`✅ Criado: /${page.slug}/index.html`);
      successCount++;

    } catch (err) {
      console.error(`❌ Erro ao gerar ${page.slug}:`, err.message);
    }
  }

  console.log(`👶 Puericultura: ${successCount}/${puericultureData.length} páginas geradas`);
  return successCount;
}

// Executar
async function main() {
  console.log('📊 Iniciando geração das NOVAS seções...\n');

  let totalPages = 0;

  // Gerar calculadoras
  totalPages += await generateCalculatorPages();

  // Gerar puericultura
  totalPages += await generatePuericulturePages();

  // Gerar intoxicações
  totalPages += await generatePoisoningPages();

  // Gerar fluxogramas
  totalPages += await generateFlowchartPages();

  // Gerar CID-10
  totalPages += await generateICDPages();

  console.log(`\n🎉 Total de páginas geradas: ${totalPages}`);
  console.log('✅ Geração das NOVAS seções concluída!');
}

/**
 * Gerar páginas HTML para intoxicações
 */
async function generatePoisoningPages() {
  console.log('\n☠️ Gerando páginas de intoxicações...');

  const distPath = path.join(process.cwd(), 'dist');
  const indexPath = path.join(distPath, 'index.html');

  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html não encontrado');
    return 0;
  }

  const baseHtml = fs.readFileSync(indexPath, 'utf8');
  let successCount = 0;

  for (const poison of poisoningsData) {
    try {
      const poisonDir = path.join(distPath, poison.slug);
      if (!fs.existsSync(poisonDir)) {
        fs.mkdirSync(poisonDir, { recursive: true });
      }

      const title = `${poison.name} | PedBook`;
      const description = poison.description;
      const keywords = poison.keywords;

      let customHtml = baseHtml;

      // Substituir meta tags
      customHtml = customHtml.replace(/<title>.*?<\/title>/, `<title>${title}</title>`);
      customHtml = customHtml.replace(/<meta name="description" content=".*?" ?\/>/, `<meta name="description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta name="keywords" content=".*?" ?\/>/, `<meta name="keywords" content="${keywords}" />`);

      // URL canônica
      const poisonUrl = `https://pedb.com.br/${poison.slug}`;
      customHtml = customHtml.replace(/<link rel="canonical" href=".*?" \/>/, `<link rel="canonical" href="${poisonUrl}" />`);

      // Open Graph
      customHtml = customHtml.replace(/<meta property="og:title" content=".*?" ?\/>/, `<meta property="og:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta property="og:description" content=".*?" ?\/>/, `<meta property="og:description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta property="og:url" content=".*?" ?\/>/, `<meta property="og:url" content="${poisonUrl}" />`);

      // Twitter Cards
      customHtml = customHtml.replace(/<meta name="twitter:title" content=".*?" ?\/>/, `<meta name="twitter:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta name="twitter:description" content=".*?" ?\/>/, `<meta name="twitter:description" content="${description}" />`);

      // Conteúdo SEO
      const seoContent = `
    <div id="root"></div>
    <!-- SEO Content for Crawlers -->
    <div style="display: none;" data-seo-content>
      <h1>${poison.name}</h1>
      <p>${poison.description}</p>
      <h2>Emergência Toxicológica</h2>
      <p>Protocolo de emergência para manejo de intoxicações em pediatria com antídotos específicos.</p>
      <h2>Antídotos e Tratamento</h2>
      <p>Informações sobre antídotos, doses pediátricas e manejo clínico especializado.</p>
    </div>`;

      customHtml = customHtml.replace('<div id="root"></div>', seoContent);

      // Schema.org
      const schema = {
        "@context": "https://schema.org",
        "@type": "MedicalWebPage",
        "name": poison.name,
        "description": description,
        "url": poisonUrl,
        "medicalAudience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde de Emergência"
        },
        "about": {
          "@type": "MedicalCondition",
          "name": poison.name,
          "medicalSpecialty": "Toxicologia"
        },
        "publisher": {
          "@type": "Organization",
          "name": "PedBook",
          "url": "https://pedb.com.br"
        }
      };

      const jsonLd = `<script type="application/ld+json">${JSON.stringify(schema, null, 2)}</script>`;
      customHtml = customHtml.replace('</head>', `  ${jsonLd}\n</head>`);

      fs.writeFileSync(path.join(poisonDir, 'index.html'), customHtml);
      console.log(`✅ Criado: /${poison.slug}/index.html`);
      successCount++;

    } catch (err) {
      console.error(`❌ Erro ao gerar ${poison.slug}:`, err.message);
    }
  }

  console.log(`☠️ Intoxicações: ${successCount}/${poisoningsData.length} páginas geradas`);
  return successCount;
}

/**
 * Gerar páginas HTML para fluxogramas
 */
async function generateFlowchartPages() {
  console.log('\n🌊 Gerando páginas de fluxogramas...');

  const distPath = path.join(process.cwd(), 'dist');
  const indexPath = path.join(distPath, 'index.html');

  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html não encontrado');
    return 0;
  }

  const baseHtml = fs.readFileSync(indexPath, 'utf8');
  let successCount = 0;

  for (const flow of flowchartsData) {
    try {
      const flowDir = path.join(distPath, flow.slug);
      if (!fs.existsSync(flowDir)) {
        fs.mkdirSync(flowDir, { recursive: true });
      }

      const title = `${flow.name} | PedBook`;
      const description = flow.description;
      const keywords = flow.keywords;

      let customHtml = baseHtml;

      // Substituir meta tags
      customHtml = customHtml.replace(/<title>.*?<\/title>/, `<title>${title}</title>`);
      customHtml = customHtml.replace(/<meta name="description" content=".*?" ?\/>/, `<meta name="description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta name="keywords" content=".*?" ?\/>/, `<meta name="keywords" content="${keywords}" />`);

      // URL canônica
      const flowUrl = `https://pedb.com.br/${flow.slug}`;
      customHtml = customHtml.replace(/<link rel="canonical" href=".*?" \/>/, `<link rel="canonical" href="${flowUrl}" />`);

      // Open Graph
      customHtml = customHtml.replace(/<meta property="og:title" content=".*?" ?\/>/, `<meta property="og:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta property="og:description" content=".*?" ?\/>/, `<meta property="og:description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta property="og:url" content=".*?" ?\/>/, `<meta property="og:url" content="${flowUrl}" />`);

      // Twitter Cards
      customHtml = customHtml.replace(/<meta name="twitter:title" content=".*?" ?\/>/, `<meta name="twitter:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta name="twitter:description" content=".*?" ?\/>/, `<meta name="twitter:description" content="${description}" />`);

      // Conteúdo SEO
      const seoContent = `
    <div id="root"></div>
    <!-- SEO Content for Crawlers -->
    <div style="display: none;" data-seo-content>
      <h1>${flow.name}</h1>
      <p>${flow.description}</p>
      <h2>Protocolo Clínico</h2>
      <p>Fluxograma baseado em evidências para diagnóstico e manejo clínico em pediatria.</p>
      <h2>Diretrizes Médicas</h2>
      <p>Protocolo estruturado para tomada de decisão clínica e manejo adequado.</p>
    </div>`;

      customHtml = customHtml.replace('<div id="root"></div>', seoContent);

      // Schema.org
      const schema = {
        "@context": "https://schema.org",
        "@type": "MedicalGuideline",
        "name": flow.name,
        "description": description,
        "url": flowUrl,
        "medicalAudience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde Pediátrica"
        },
        "about": {
          "@type": "MedicalCondition",
          "name": flow.name,
          "medicalSpecialty": "Pediatria"
        },
        "publisher": {
          "@type": "Organization",
          "name": "PedBook",
          "url": "https://pedb.com.br"
        }
      };

      const jsonLd = `<script type="application/ld+json">${JSON.stringify(schema, null, 2)}</script>`;
      customHtml = customHtml.replace('</head>', `  ${jsonLd}\n</head>`);

      fs.writeFileSync(path.join(flowDir, 'index.html'), customHtml);
      console.log(`✅ Criado: /${flow.slug}/index.html`);
      successCount++;

    } catch (err) {
      console.error(`❌ Erro ao gerar ${flow.slug}:`, err.message);
    }
  }

  console.log(`🌊 Fluxogramas: ${successCount}/${flowchartsData.length} páginas geradas`);
  return successCount;
}

/**
 * Gerar páginas HTML para CID-10
 */
async function generateICDPages() {
  console.log('\n🏥 Gerando páginas de CID-10...');

  const distPath = path.join(process.cwd(), 'dist');
  const indexPath = path.join(distPath, 'index.html');

  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html não encontrado');
    return 0;
  }

  const baseHtml = fs.readFileSync(indexPath, 'utf8');
  let successCount = 0;

  for (const icd of icdData) {
    try {
      const icdDir = path.join(distPath, icd.slug);
      if (!fs.existsSync(icdDir)) {
        fs.mkdirSync(icdDir, { recursive: true });
      }

      const title = `${icd.name} | PedBook`;
      const description = icd.description;
      const keywords = icd.keywords;

      let customHtml = baseHtml;

      // Substituir meta tags
      customHtml = customHtml.replace(/<title>.*?<\/title>/, `<title>${title}</title>`);
      customHtml = customHtml.replace(/<meta name="description" content=".*?" ?\/>/, `<meta name="description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta name="keywords" content=".*?" ?\/>/, `<meta name="keywords" content="${keywords}" />`);

      // URL canônica
      const icdUrl = `https://pedb.com.br/${icd.slug}`;
      customHtml = customHtml.replace(/<link rel="canonical" href=".*?" \/>/, `<link rel="canonical" href="${icdUrl}" />`);

      // Open Graph
      customHtml = customHtml.replace(/<meta property="og:title" content=".*?" ?\/>/, `<meta property="og:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta property="og:description" content=".*?" ?\/>/, `<meta property="og:description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta property="og:url" content=".*?" ?\/>/, `<meta property="og:url" content="${icdUrl}" />`);

      // Twitter Cards
      customHtml = customHtml.replace(/<meta name="twitter:title" content=".*?" ?\/>/, `<meta name="twitter:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta name="twitter:description" content=".*?" ?\/>/, `<meta name="twitter:description" content="${description}" />`);

      // Conteúdo SEO
      const seoContent = `
    <div id="root"></div>
    <!-- SEO Content for Crawlers -->
    <div style="display: none;" data-seo-content>
      <h1>${icd.name}</h1>
      <p>${icd.description}</p>
      <h2>Classificação Internacional de Doenças</h2>
      <p>Sistema de codificação médica para diagnósticos pediátricos conforme padrões internacionais.</p>
      <h2>Códigos Diagnósticos</h2>
      <p>Ferramenta para busca e consulta de códigos CID-10 específicos para pediatria.</p>
    </div>`;

      customHtml = customHtml.replace('<div id="root"></div>', seoContent);

      // Schema.org
      const schema = {
        "@context": "https://schema.org",
        "@type": "MedicalWebPage",
        "name": icd.name,
        "description": description,
        "url": icdUrl,
        "medicalAudience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde"
        },
        "about": {
          "@type": "MedicalCondition",
          "name": "Classificação de Doenças",
          "medicalSpecialty": "Medicina Geral"
        },
        "publisher": {
          "@type": "Organization",
          "name": "PedBook",
          "url": "https://pedb.com.br"
        }
      };

      const jsonLd = `<script type="application/ld+json">${JSON.stringify(schema, null, 2)}</script>`;
      customHtml = customHtml.replace('</head>', `  ${jsonLd}\n</head>`);

      fs.writeFileSync(path.join(icdDir, 'index.html'), customHtml);
      console.log(`✅ Criado: /${icd.slug}/index.html`);
      successCount++;

    } catch (err) {
      console.error(`❌ Erro ao gerar ${icd.slug}:`, err.message);
    }
  }

  console.log(`🏥 CID-10: ${successCount}/${icdData.length} páginas geradas`);
  return successCount;
}

main().catch(console.error);
