/**
 * Script para gerar páginas SEO das NOVAS seções implementadas
 * (Calculadoras, Puericultura, Intoxicações, Fluxogramas, CID-10)
 */

import fs from 'fs';
import path from 'path';

console.log('🚀 Gerando páginas SEO das NOVAS seções...');

/**
 * Dados das calculadoras
 */
const calculatorsData = [
  {
    slug: 'imc',
    name: 'Calculadora de IMC Pediátrico',
    description: 'Calcule o Índice de Massa Corporal (IMC) para crianças e adolescentes com percentis específicos para idade.',
    keywords: 'imc pediatrico, indice massa corporal crianca, percentil imc, obesidade infantil, desnutricao pediatrica'
  },
  {
    slug: 'hidratacao',
    name: 'Calculadora de Hidratação',
    description: 'Calcule necessidades hídricas e reposição de fluidos em pediatria com base no peso e grau de desidratação.',
    keywords: 'hidratacao pediatrica, reposicao fluidos, desidratacao crianca, soro fisiologico, eletroliticos'
  },
  {
    slug: 'superficie-corporal',
    name: '<PERSON>culador<PERSON> de Superfície Corporal',
    description: 'Calcule a área de superfície corporal (ASC) pediátrica usando fórmulas validadas para dosagem de medicamentos.',
    keywords: 'superficie corporal pediatrica, asc crianca, area superficie corporal, dosagem medicamento'
  },
  {
    slug: 'apgar',
    name: 'Calculadora de Apgar',
    description: 'Avalie o estado do recém-nascido através do escore de Apgar nos primeiros minutos de vida.',
    keywords: 'apgar score, escore apgar, avaliacao recem nascido, vitalidade neonatal'
  },
  {
    slug: 'glasgow',
    name: 'Escala de Glasgow Pediátrica',
    description: 'Avalie o nível de consciência em crianças usando a Escala de Coma de Glasgow adaptada para pediatria.',
    keywords: 'glasgow pediatrico, escala coma glasgow, nivel consciencia crianca, trauma craniano'
  },
  {
    slug: 'capurro',
    name: 'Método de Capurro',
    description: 'Determine a idade gestacional do recém-nascido através do método de Capurro com avaliação somática.',
    keywords: 'capurro metodo, idade gestacional, avaliacao somatica, recem nascido prematuro'
  },
  {
    slug: 'ballard',
    name: 'Método de Ballard',
    description: 'Avalie a maturidade do recém-nascido através do método de Ballard com critérios neurológicos e físicos.',
    keywords: 'ballard score, maturidade neonatal, idade gestacional ballard, avaliacao neurologica'
  },
  {
    slug: 'finnegan',
    name: 'Escala de Finnegan',
    description: 'Avalie síndrome de abstinência neonatal através da escala de Finnegan para recém-nascidos.',
    keywords: 'finnegan escala, sindrome abstinencia neonatal, abstinencia drogas recem nascido'
  },
  {
    slug: 'bhutani',
    name: 'Nomograma de Bhutani',
    description: 'Avalie o risco de hiperbilirrubinemia em recém-nascidos através do nomograma de Bhutani.',
    keywords: 'bhutani nomograma, hiperbilirrubinemia, ictericia neonatal, bilirrubina recem nascido'
  },
  {
    slug: 'rodwell',
    name: 'Calculadora de Rodwell',
    description: 'Avalie parâmetros hematológicos em recém-nascidos através dos valores de referência de Rodwell.',
    keywords: 'rodwell valores, hematologia neonatal, hemograma recem nascido, valores referencia'
  },
  {
    slug: 'gina',
    name: 'Calculadora GINA',
    description: 'Avalie e classifique a asma pediátrica seguindo as diretrizes GINA (Global Initiative for Asthma).',
    keywords: 'gina asma, classificacao asma pediatrica, controle asma crianca, diretrizes gina'
  }
];

/**
 * Dados da puericultura
 */
const puericultureData = [
  {
    slug: 'puericultura',
    name: 'Puericultura',
    description: 'Acompanhamento completo do desenvolvimento infantil com ferramentas para consultas de puericultura.',
    keywords: 'puericultura, acompanhamento infantil, desenvolvimento crianca, consulta pediatrica'
  },
  {
    slug: 'puericultura/curva-de-crescimento',
    name: 'Curvas de Crescimento',
    description: 'Monitore o crescimento infantil com curvas de peso, altura e perímetro cefálico da OMS.',
    keywords: 'curva crescimento, percentil peso altura, oms crescimento, desenvolvimento fisico'
  },
  {
    slug: 'puericultura/calendario-vacinal',
    name: 'Calendário Vacinal',
    description: 'Calendário de vacinação infantil atualizado com todas as vacinas recomendadas pelo Ministério da Saúde.',
    keywords: 'calendario vacinal, vacinas crianca, imunizacao pediatrica, esquema vacinal'
  },
  {
    slug: 'puericultura/formulas',
    name: 'Fórmulas Infantis',
    description: 'Guia completo sobre fórmulas infantis, preparo e indicações para alimentação de lactentes.',
    keywords: 'formula infantil, leite artificial, alimentacao lactente, preparo mamadeira'
  },
  {
    slug: 'puericultura/suplementacao-infantil',
    name: 'Suplementação Infantil',
    description: 'Orientações sobre suplementação vitamínica e mineral em crianças conforme faixa etária.',
    keywords: 'suplementacao infantil, vitaminas crianca, ferro pediatrico, vitamina d'
  },
  {
    slug: 'puericultura/patient-overview',
    name: 'Visão Geral do Paciente',
    description: 'Ferramenta para avaliação global do paciente pediátrico com dados antropométricos e desenvolvimento.',
    keywords: 'avaliacao pediatrica, dados antropometricos, desenvolvimento neuropsicomotor'
  },
  {
    slug: 'dnpm',
    name: 'DNPM - Desenvolvimento Neuropsicomotor',
    description: 'Avalie marcos do desenvolvimento neuropsicomotor em crianças por faixa etária.',
    keywords: 'dnpm, desenvolvimento neuropsicomotor, marcos desenvolvimento, atraso desenvolvimento'
  }
];

/**
 * Dados das intoxicações
 */
const poisoningsData = [
  {
    slug: 'poisonings',
    name: 'Intoxicações Pediátricas',
    description: 'Guia completo para manejo de intoxicações em pediatria com antídotos e doses específicas.',
    keywords: 'intoxicacao pediatrica, envenenamento crianca, antidotos pediatricos, emergencia toxicologica'
  },
  {
    slug: 'poisonings/benzodiazepinicos',
    name: 'Intoxicação por Benzodiazepínicos',
    description: 'Manejo da intoxicação por benzodiazepínicos em crianças: sintomas, antídoto flumazenil e doses.',
    keywords: 'benzodiazepinicos intoxicacao, flumazenil pediatrico, overdose benzodiazepinicos'
  },
  {
    slug: 'poisonings/opioides',
    name: 'Intoxicação por Opioides',
    description: 'Tratamento da intoxicação por opioides em pediatria: naloxona, doses e manejo da depressão respiratória.',
    keywords: 'opioides intoxicacao, naloxona pediatrica, overdose opioides, depressao respiratoria'
  }
];

/**
 * Gerar páginas HTML para calculadoras
 */
async function generateCalculatorPages() {
  console.log('\n🧮 Gerando páginas de calculadoras...');
  
  const distPath = path.join(process.cwd(), 'dist');
  const indexPath = path.join(distPath, 'index.html');
  
  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html não encontrado');
    return 0;
  }
  
  const baseHtml = fs.readFileSync(indexPath, 'utf8');
  let successCount = 0;
  
  for (const calc of calculatorsData) {
    try {
      const calcDir = path.join(distPath, 'calculadoras', calc.slug);
      if (!fs.existsSync(calcDir)) {
        fs.mkdirSync(calcDir, { recursive: true });
      }
      
      const title = `${calc.name} | PedBook`;
      const description = calc.description;
      const keywords = calc.keywords;
      
      let customHtml = baseHtml;
      
      // Substituir meta tags
      customHtml = customHtml.replace(/<title>.*?<\/title>/, `<title>${title}</title>`);
      customHtml = customHtml.replace(/<meta name="description" content=".*?" ?\/>/, `<meta name="description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta name="keywords" content=".*?" ?\/>/, `<meta name="keywords" content="${keywords}" />`);
      
      // URL canônica
      const calcUrl = `https://pedb.com.br/calculadoras/${calc.slug}`;
      customHtml = customHtml.replace(/<link rel="canonical" href=".*?" \/>/, `<link rel="canonical" href="${calcUrl}" />`);
      
      // Open Graph
      customHtml = customHtml.replace(/<meta property="og:title" content=".*?" ?\/>/, `<meta property="og:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta property="og:description" content=".*?" ?\/>/, `<meta property="og:description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta property="og:url" content=".*?" ?\/>/, `<meta property="og:url" content="${calcUrl}" />`);
      
      // Twitter Cards
      customHtml = customHtml.replace(/<meta name="twitter:title" content=".*?" ?\/>/, `<meta name="twitter:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta name="twitter:description" content=".*?" ?\/>/, `<meta name="twitter:description" content="${description}" />`);
      
      // Conteúdo SEO
      const seoContent = `
    <div id="root"></div>
    <!-- SEO Content for Crawlers -->
    <div style="display: none;" data-seo-content>
      <h1>${calc.name}</h1>
      <p>${calc.description}</p>
      <h2>Calculadora Pediátrica Automática</h2>
      <p>Ferramenta especializada para cálculos em pediatria com resultados precisos e baseados em evidências científicas.</p>
      <h2>Como Usar</h2>
      <p>Insira os dados solicitados e obtenha resultados automáticos com interpretação clínica adequada para pediatria.</p>
    </div>`;
      
      customHtml = customHtml.replace('<div id="root"></div>', seoContent);
      
      // Schema.org
      const schema = {
        "@context": "https://schema.org",
        "@type": "MedicalWebPage",
        "name": calc.name,
        "description": description,
        "url": calcUrl,
        "medicalAudience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde Pediátrica"
        },
        "about": {
          "@type": "MedicalProcedure",
          "name": calc.name,
          "medicalSpecialty": "Pediatria"
        },
        "publisher": {
          "@type": "Organization",
          "name": "PedBook",
          "url": "https://pedb.com.br"
        }
      };
      
      const jsonLd = `<script type="application/ld+json">${JSON.stringify(schema, null, 2)}</script>`;
      customHtml = customHtml.replace('</head>', `  ${jsonLd}\n</head>`);
      
      fs.writeFileSync(path.join(calcDir, 'index.html'), customHtml);
      console.log(`✅ Criado: /calculadoras/${calc.slug}/index.html`);
      successCount++;
      
    } catch (err) {
      console.error(`❌ Erro ao gerar ${calc.slug}:`, err.message);
    }
  }
  
  console.log(`🧮 Calculadoras: ${successCount}/${calculatorsData.length} páginas geradas`);
  return successCount;
}

// Executar
async function main() {
  console.log('📊 Iniciando geração das NOVAS seções...\n');
  
  let totalPages = 0;
  
  // Gerar calculadoras
  totalPages += await generateCalculatorPages();
  
  console.log(`\n🎉 Total de páginas geradas: ${totalPages}`);
  console.log('✅ Geração das NOVAS seções concluída!');
}

main().catch(console.error);
