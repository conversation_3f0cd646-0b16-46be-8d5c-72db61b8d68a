
import { useState } from "react";
import { Link } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart<PERSON>, <PERSON>, <PERSON>, <PERSON>, Brain, PillBottle, Clock, Info } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { PatientForm } from "@/components/patient-overview/PatientForm";
import { PatientDashboard } from "@/components/patient-overview/PatientDashboard";
import { Card } from "@/components/ui/card";
import { PatientOverviewBanner } from "@/components/patient-overview/PatientOverviewBanner";

export default function PatientOverview() {
  const [showDashboard, setShowDashboard] = useState(false);
  const [patientData, setPatientData] = useState<any>(null);

  const handlePatientDataSubmit = (data: any) => {
    console.log("🧒 Dados do paciente submetidos:", data);
    setPatientData(data);
    setShowDashboard(true);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800">
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8 md:py-12">
        <Link 
          to="/puericultura" 
          className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors mb-8 dark:text-blue-400 dark:hover:text-blue-300"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Voltar para Puericultura</span>
        </Link>

        {!showDashboard ? (
          <>
            <PatientOverviewBanner />
            
            <Card className="max-w-2xl mx-auto p-6 mt-8 border border-primary/10 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm shadow-lg">
              <h2 className="text-xl font-bold mb-4 text-primary dark:text-blue-400">
                Preencha os dados do paciente
              </h2>
              <PatientForm onSubmit={handlePatientDataSubmit} />
            </Card>
          </>
        ) : (
          <PatientDashboard data={patientData} onBack={() => setShowDashboard(false)} />
        )}
      </main>

      <Footer />
    </div>
  );
}
