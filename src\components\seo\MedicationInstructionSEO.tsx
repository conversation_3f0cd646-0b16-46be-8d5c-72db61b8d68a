import HelmetWrapper from "@/components/utils/HelmetWrapper";

interface MedicationInstructionSEOProps {
  medicationName: string;
  medicationSlug: string;
  description?: string;
  brands?: string;
  category?: string;
  hasInstructions: boolean;
}

export const MedicationInstructionSEO = ({
  medicationName,
  medicationSlug,
  description,
  brands,
  category,
  hasInstructions
}: MedicationInstructionSEOProps) => {
  
  // Gerar título dinâmico
  const dynamicTitle = hasInstructions 
    ? `Bula Profissional ${medicationName} | Posologia e Indicações Pediátricas`
    : `${medicationName} - Bula Profissional | PedBook`;

  // Gerar descrição dinâmica
  const generateDescription = () => {
    if (!hasInstructions) {
      return `Bula profissional do ${medicationName} para uso pediátrico. Informações sobre posologia, indicações, contraindicações e cuidados especiais em pediatria.`;
    }

    let desc = `Bula profissional completa do ${medicationName} para pediatria. `;
    
    if (description) {
      desc += `${description.substring(0, 100)}... `;
    }
    
    desc += `Posologia pediátrica, indicações, contraindicações, cuidados especiais e orientações para profissionais da saúde.`;
    
    if (brands) {
      desc += ` Nomes comerciais: ${brands.split(',').slice(0, 3).join(', ')}.`;
    }

    return desc.substring(0, 160);
  };

  // Gerar keywords dinâmicas
  const generateKeywords = () => {
    const baseKeywords = [
      `bula ${medicationName.toLowerCase()}`,
      `${medicationName.toLowerCase()} pediátrico`,
      `posologia ${medicationName.toLowerCase()}`,
      `${medicationName.toLowerCase()} pediatria`,
      "bula profissional",
      "medicamento pediátrico",
      "posologia pediátrica",
      "indicações pediátricas",
      "contraindicações pediatria"
    ];

    if (category) {
      baseKeywords.push(`${category.toLowerCase()} pediátrico`);
    }

    if (brands) {
      const brandList = brands.split(',').slice(0, 5);
      brandList.forEach(brand => {
        baseKeywords.push(`bula ${brand.trim().toLowerCase()}`);
      });
    }

    return baseKeywords.join(", ");
  };

  // URL canônica
  const canonicalUrl = `https://pedb.com.br/bulas-profissionais/${medicationSlug}`;

  // Schema.org para medicamento
  const medicationSchema = {
    "@context": "https://schema.org",
    "@type": "Drug",
    "name": medicationName,
    "description": generateDescription(),
    "url": canonicalUrl,
    "manufacturer": {
      "@type": "Organization",
      "name": "Diversos fabricantes"
    },
    "activeIngredient": medicationName,
    "dosageForm": "Conforme prescrição médica",
    "administrationRoute": "Conforme indicação médica",
    "targetPopulation": "Pacientes pediátricos",
    "prescriptionStatus": "PrescriptionOnly",
    "availableStrength": "Diversas concentrações",
    "clinicalPharmacology": description || `Informações clínicas do ${medicationName}`,
    "indication": `Uso pediátrico conforme prescrição médica`,
    "contraindication": "Conforme bula profissional",
    "dosage": "Conforme peso e idade do paciente",
    "overdose": "Procurar atendimento médico imediato",
    "pregnancyCategory": "Consultar orientações específicas",
    "breastfeedingWarning": "Consultar orientações para amamentação"
  };

  // Schema.org para página médica
  const medicalPageSchema = {
    "@context": "https://schema.org",
    "@type": "MedicalWebPage",
    "name": dynamicTitle,
    "description": generateDescription(),
    "url": canonicalUrl,
    "mainContentOfPage": {
      "@type": "WebPageElement",
      "cssSelector": "main"
    },
    "specialty": "Pediatria",
    "audience": {
      "@type": "MedicalAudience",
      "audienceType": "Médicos pediatras e profissionais da saúde"
    },
    "about": {
      "@type": "Drug",
      "name": medicationName
    },
    "lastReviewed": new Date().toISOString().split('T')[0],
    "reviewedBy": {
      "@type": "Organization",
      "name": "PedBook",
      "url": "https://pedb.com.br"
    }
  };

  // Breadcrumb Schema
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "PedBook",
        "item": "https://pedb.com.br"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Bulas Profissionais",
        "item": "https://pedb.com.br/bulas-profissionais"
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": medicationName,
        "item": canonicalUrl
      }
    ]
  };

  return (
    <HelmetWrapper>
      {/* Título e Descrição Dinâmicos */}
      <title>{dynamicTitle}</title>
      <meta name="description" content={generateDescription()} />
      <meta name="keywords" content={generateKeywords()} />

      {/* Meta tags médicas específicas */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="medical-content" content="professional" />
      <meta name="target-audience" content="healthcare-professionals" />
      <meta name="content-type" content="drug-information" />
      
      {/* Geo targeting */}
      <meta name="geo.region" content="BR" />
      <meta name="geo.country" content="Brazil" />
      <meta name="language" content="Portuguese" />

      {/* Open Graph */}
      <meta property="og:title" content={dynamicTitle} />
      <meta property="og:description" content={generateDescription()} />
      <meta property="og:type" content="article" />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:image" content="https://pedb.com.br/faviconx.webp" />
      <meta property="og:image:alt" content={`Bula ${medicationName} - PedBook`} />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />
      <meta property="article:section" content="Medicina" />
      <meta property="article:tag" content="Pediatria" />
      <meta property="article:tag" content="Medicamentos" />
      <meta property="article:tag" content="Bulas" />
      {category && <meta property="article:tag" content={category} />}

      {/* Twitter Cards */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={dynamicTitle} />
      <meta name="twitter:description" content={generateDescription()} />
      <meta name="twitter:image" content="https://pedb.com.br/faviconx.webp" />
      <meta name="twitter:site" content="@pedbook" />

      {/* Canonical */}
      <link rel="canonical" href={canonicalUrl} />

      {/* Schema.org - Medicamento */}
      <script type="application/ld+json">
        {JSON.stringify(medicationSchema)}
      </script>

      {/* Schema.org - Página Médica */}
      <script type="application/ld+json">
        {JSON.stringify(medicalPageSchema)}
      </script>

      {/* Schema.org - Breadcrumb */}
      <script type="application/ld+json">
        {JSON.stringify(breadcrumbSchema)}
      </script>

      {/* Preconnect para performance */}
      <link rel="preconnect" href="https://bxedpdmgvgatjdfxgxij.supabase.co" />
      <link rel="dns-prefetch" href="https://bxedpdmgvgatjdfxgxij.supabase.co" />
    </HelmetWrapper>
  );
};
