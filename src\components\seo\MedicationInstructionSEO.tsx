import HelmetWrapper from "@/components/utils/HelmetWrapper";

interface MedicationInstructionSEOProps {
  medicationName: string;
  medicationSlug: string;
  description?: string;
  brands?: string;
  category?: string;
  hasInstructions: boolean;
  instructionContent?: string;
}

export const MedicationInstructionSEO = ({
  medicationName,
  medicationSlug,
  description,
  brands,
  category,
  hasInstructions,
  instructionContent
}: MedicationInstructionSEOProps) => {

  // Extrair dados específicos estruturados do conteúdo da bula
  const extractMedicationData = (content: string) => {
    if (!content) return {};

    const data: any = {};

    // Extrair DCB
    const dcbMatch = content.match(/<strong>Nome Genérico \(DCB\):<\/strong>\s*([^<\n\r]+)/i);
    if (dcbMatch) data.dcb = dcbMatch[1].trim();

    // Extrair DCI
    const dciMatch = content.match(/<strong>Nome Internacional \(DCI\):<\/strong>\s*([^<\n\r]+)/i);
    if (dciMatch) data.dci = dciMatch[1].trim();

    // Extrair Classe Terapêutica
    const classMatch = content.match(/<strong>Classe Terapêutica:<\/strong>\s*([^<\n\r]+)/i);
    if (classMatch) data.therapeuticClass = classMatch[1].trim();

    // Extrair Código ATC
    const atcMatch = content.match(/<strong>Código ATC:<\/strong>\s*([^<\n\r]+)/i);
    if (atcMatch) data.atcCode = atcMatch[1].trim();

    // Extrair Nomes Comerciais do conteúdo
    const commercialMatch = content.match(/<strong>Nomes comerciais:<\/strong>\s*([^<\n\r]+)/i);
    if (commercialMatch) data.commercialNames = commercialMatch[1].trim();

    // Extrair Mecanismo de Ação (seção completa)
    const mechanismMatch = content.match(/##\.\s*Mecanismo de Ação<\/p><p>([\s\S]*?)(?=<p>##\.|$)/i);
    if (mechanismMatch) {
      data.mechanism = mechanismMatch[1].replace(/<[^>]*>/g, '').trim().substring(0, 200);
    }

    // Extrair Apresentações Comerciais
    const presentationsMatch = content.match(/##\.\s*Apresentações Comerciais<\/p>([\s\S]*?)(?=<p>##\.|$)/i);
    if (presentationsMatch) {
      const presentations = presentationsMatch[1].replace(/<[^>]*>/g, '').trim();
      data.presentations = presentations.substring(0, 150);
    }

    // Extrair Indicações Clínicas
    const indicationsMatch = content.match(/##\.\s*Indicações Clínicas<\/p>([\s\S]*?)(?=<p>##\.|$)/i);
    if (indicationsMatch) {
      const indications = indicationsMatch[1].replace(/<[^>]*>/g, '').trim();
      data.indications = indications.substring(0, 200);
    }

    // Extrair Posologia resumida
    const dosageMatch = content.match(/##\.\s*Posologia e Modo de Uso<\/p>([\s\S]*?)(?=<p>##\.|$)/i);
    if (dosageMatch) {
      const dosage = dosageMatch[1].replace(/<[^>]*>/g, '').trim();
      data.dosage = dosage.substring(0, 150);
    }

    // Extrair Contraindicações
    const contraindicationsMatch = content.match(/##\.\s*Contraindicações<\/p>([\s\S]*?)(?=<p>##\.|$)/i);
    if (contraindicationsMatch) {
      const contraindications = contraindicationsMatch[1].replace(/<[^>]*>/g, '').trim();
      data.contraindications = contraindications.substring(0, 150);
    }

    return data;
  };

  const medicationData = extractMedicationData(instructionContent || '');
  
  // Gerar título dinâmico com dados específicos
  const generateTitle = () => {
    let title = `Bula ${medicationName}`;

    if (medicationData.atcCode) {
      title += ` (${medicationData.atcCode})`;
    }

    if (medicationData.therapeuticClass) {
      title += ` | ${medicationData.therapeuticClass}`;
    } else if (category) {
      title += ` | ${category}`;
    }

    title += ` - Posologia Pediátrica`;

    return title.substring(0, 60); // Limite do Google
  };

  const dynamicTitle = generateTitle();

  // Gerar descrição dinâmica com dados específicos extraídos
  const generateDescription = () => {
    let desc = `Bula profissional ${medicationName}`;

    // Adicionar DCB/DCI se disponível
    if (medicationData.dcb || medicationData.dci) {
      desc += ` (${medicationData.dcb || medicationData.dci})`;
    }

    desc += ` para pediatria. `;

    // Adicionar classe terapêutica específica
    if (medicationData.therapeuticClass) {
      desc += `${medicationData.therapeuticClass}. `;
    } else if (category) {
      desc += `${category}. `;
    }

    // Adicionar código ATC
    if (medicationData.atcCode) {
      desc += `ATC: ${medicationData.atcCode}. `;
    }

    // Adicionar indicações principais
    if (medicationData.indications) {
      const cleanIndications = medicationData.indications.replace(/[✅⚠️]/g, '').trim();
      desc += `Indicado para ${cleanIndications.substring(0, 50)}... `;
    }

    desc += `Posologia, contraindicações e orientações para profissionais da saúde.`;

    // Adicionar nomes comerciais
    const commercialNames = medicationData.commercialNames || brands;
    if (commercialNames) {
      const brandList = commercialNames.split(',').slice(0, 2).map(b => b.trim().replace(/®/g, ''));
      desc += ` Nomes comerciais: ${brandList.join(', ')}.`;
    }

    return desc.substring(0, 160);
  };



  // Gerar keywords dinâmicas com dados específicos
  const generateKeywords = () => {
    const baseKeywords = [
      `bula ${medicationName.toLowerCase()}`,
      `${medicationName.toLowerCase()} pediátrico`,
      `posologia ${medicationName.toLowerCase()}`,
      `${medicationName.toLowerCase()} pediatria`,
      "bula profissional",
      "medicamento pediátrico",
      "posologia pediátrica",
      "indicações pediátricas",
      "contraindicações pediatria"
    ];

    // Adicionar DCB/DCI
    if (medicationData.dcb) {
      baseKeywords.push(`dcb ${medicationData.dcb}`);
      baseKeywords.push(`${medicationData.dcb} pediátrico`);
    }
    if (medicationData.dci) {
      baseKeywords.push(`dci ${medicationData.dci.toLowerCase()}`);
      baseKeywords.push(`${medicationData.dci.toLowerCase()} pediatria`);
    }

    // Adicionar código ATC
    if (medicationData.atcCode) {
      baseKeywords.push(`atc ${medicationData.atcCode.toLowerCase()}`);
      baseKeywords.push(`código atc ${medicationData.atcCode.toLowerCase()}`);
    }

    // Adicionar classe terapêutica específica
    if (medicationData.therapeuticClass) {
      baseKeywords.push(`${medicationData.therapeuticClass.toLowerCase()}`);
      baseKeywords.push(`${medicationData.therapeuticClass.toLowerCase()} pediátrico`);
    } else if (category) {
      baseKeywords.push(`${category.toLowerCase()} pediátrico`);
    }

    // Adicionar nomes comerciais
    const commercialNames = medicationData.commercialNames || brands;
    if (commercialNames) {
      const brandList = commercialNames.split(',').slice(0, 8);
      brandList.forEach(brand => {
        const cleanBrand = brand.trim().replace(/®/g, '').toLowerCase();
        baseKeywords.push(`bula ${cleanBrand}`);
        baseKeywords.push(`${cleanBrand} pediátrico`);
      });
    }

    return baseKeywords.join(", ");
  };

  // URL canônica
  const canonicalUrl = `https://pedb.com.br/bulas-profissionais/${medicationSlug}`;

  // Schema.org para medicamento com dados específicos
  const medicationSchema = {
    "@context": "https://schema.org",
    "@type": "Drug",
    "name": medicationName,
    "description": generateDescription(),
    "url": canonicalUrl,
    "manufacturer": {
      "@type": "Organization",
      "name": "Diversos fabricantes"
    },
    "activeIngredient": medicationData.dcb || medicationData.dci || medicationName,
    "dosageForm": medicationData.presentations || "Conforme prescrição médica",
    "administrationRoute": "Conforme indicação médica",
    "targetPopulation": "Pacientes pediátricos",
    "prescriptionStatus": "PrescriptionOnly",
    "availableStrength": medicationData.presentations || "Diversas concentrações",
    "clinicalPharmacology": medicationData.mechanism || `Informações clínicas do ${medicationName}`,
    "indication": medicationData.indications || "Uso pediátrico conforme prescrição médica",
    "contraindication": medicationData.contraindications || "Conforme bula profissional",
    "dosage": medicationData.dosage || "Conforme peso e idade do paciente",
    "overdose": "Procurar atendimento médico imediato",
    "pregnancyCategory": "Consultar orientações específicas",
    "breastfeedingWarning": "Consultar orientações para amamentação",
    ...(medicationData.atcCode && { "code": medicationData.atcCode }),
    ...(medicationData.therapeuticClass && { "drugClass": medicationData.therapeuticClass }),
    ...(medicationData.commercialNames && {
      "tradeName": medicationData.commercialNames.split(',').slice(0, 5).map(name => name.trim())
    })
  };

  // Schema.org para página médica
  const medicalPageSchema = {
    "@context": "https://schema.org",
    "@type": "MedicalWebPage",
    "name": dynamicTitle,
    "description": generateDescription(),
    "url": canonicalUrl,
    "mainContentOfPage": {
      "@type": "WebPageElement",
      "cssSelector": "main"
    },
    "specialty": "Pediatria",
    "audience": {
      "@type": "MedicalAudience",
      "audienceType": "Médicos pediatras e profissionais da saúde"
    },
    "about": {
      "@type": "Drug",
      "name": medicationName
    },
    "lastReviewed": new Date().toISOString().split('T')[0],
    "reviewedBy": {
      "@type": "Organization",
      "name": "PedBook",
      "url": "https://pedb.com.br"
    }
  };

  // Breadcrumb Schema
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "PedBook",
        "item": "https://pedb.com.br"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Bulas Profissionais",
        "item": "https://pedb.com.br/bulas-profissionais"
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": medicationName,
        "item": canonicalUrl
      }
    ]
  };

  return (
    <HelmetWrapper>
      {/* Título e Descrição Dinâmicos */}
      <title>{dynamicTitle}</title>
      <meta name="description" content={generateDescription()} />
      <meta name="keywords" content={generateKeywords()} />

      {/* Meta tags médicas específicas */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="medical-content" content="professional" />
      <meta name="target-audience" content="healthcare-professionals" />
      <meta name="content-type" content="drug-information" />
      
      {/* Geo targeting */}
      <meta name="geo.region" content="BR" />
      <meta name="geo.country" content="Brazil" />
      <meta name="language" content="Portuguese" />

      {/* Open Graph */}
      <meta property="og:title" content={dynamicTitle} />
      <meta property="og:description" content={generateDescription()} />
      <meta property="og:type" content="article" />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:image" content="https://pedb.com.br/faviconx.webp" />
      <meta property="og:image:alt" content={`Bula ${medicationName} - PedBook`} />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />
      <meta property="article:section" content="Medicina" />
      <meta property="article:tag" content="Pediatria" />
      <meta property="article:tag" content="Medicamentos" />
      <meta property="article:tag" content="Bulas" />
      {category && <meta property="article:tag" content={category} />}

      {/* Twitter Cards */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={dynamicTitle} />
      <meta name="twitter:description" content={generateDescription()} />
      <meta name="twitter:image" content="https://pedb.com.br/faviconx.webp" />
      <meta name="twitter:site" content="@pedbook" />

      {/* Canonical */}
      <link rel="canonical" href={canonicalUrl} />

      {/* Schema.org - Medicamento */}
      <script type="application/ld+json">
        {JSON.stringify(medicationSchema)}
      </script>

      {/* Schema.org - Página Médica */}
      <script type="application/ld+json">
        {JSON.stringify(medicalPageSchema)}
      </script>

      {/* Schema.org - Breadcrumb */}
      <script type="application/ld+json">
        {JSON.stringify(breadcrumbSchema)}
      </script>

      {/* Preconnect para performance */}
      <link rel="preconnect" href="https://bxedpdmgvgatjdfxgxij.supabase.co" />
      <link rel="dns-prefetch" href="https://bxedpdmgvgatjdfxgxij.supabase.co" />
    </HelmetWrapper>
  );
};
