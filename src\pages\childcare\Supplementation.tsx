
import { ArrowLeft } from "lucide-react";
import { Link } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { SupplementationForm } from "@/components/supplementation/SupplementationForm";
import { SupplementationResultView } from "@/components/supplementation/SupplementationResult";
import SupplementationFAQ from "@/components/supplementation/SupplementationFAQ";
import { useState } from "react";
import { SupplementationInput, SupplementationResult } from "@/types/supplementation";
import { calculateSupplementation } from "@/utils/supplementationCalculator";
import { SupplementationMetaTags } from "@/components/childcare/supplementation/SupplementationMetaTags";

const Supplementation = () => {
  const [result, setResult] = useState<SupplementationResult | null>(null);

  const handleCalculate = (input: SupplementationInput) => {
    const calculatedResult = calculateSupplementation(input);
    setResult(calculatedResult);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <SupplementationMetaTags />
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-12">
        <Link 
          to="/puericultura" 
          className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors mb-8 hover:translate-x-1 transform duration-200 dark:text-blue-400 dark:hover:text-blue-300"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Voltar para Puericultura</span>
        </Link>

        <div className="text-center space-y-4 mb-12">
          <div className="relative inline-block perspective preserve-3d group">
            <h1 className="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-blue-400 relative z-10 transform transition-transform duration-300 group-hover:scale-105">
              Suplementação
            </h1>
            <div className="absolute -inset-4 bg-blue-100/50 blur-xl rounded-full -z-10 group-hover:blur-2xl transition-all duration-300 dark:bg-blue-900/30" />
          </div>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto animate-fade-in-up">
            Descubra os nutrientes recomendados para seu paciente.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto mb-16">
          <div className="space-y-6 glass-card backdrop-blur-sm p-6 rounded-lg shadow-lg transition-all duration-300 hover:shadow-xl bg-white/80 dark:bg-slate-800/80 dark:border dark:border-slate-700">
            <SupplementationForm onCalculate={handleCalculate} />
          </div>

          <div className="space-y-4">
            {result ? (
              <SupplementationResultView result={result} isVisible={true} />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-4 p-8 glass-card backdrop-blur-sm rounded-lg animate-fade-in-up bg-white/80 dark:bg-slate-800/80 dark:border dark:border-slate-700">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-900 flex items-center justify-center">
                    <svg 
                      className="w-8 h-8 text-blue-500 dark:text-blue-400" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth={2} 
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" 
                      />
                    </svg>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300">
                    Preencha os campos com as informações necessárias para calcularmos a suplementação adequada para a criança
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="max-w-4xl mx-auto transform transition-all duration-300 hover:translate-y-[-4px]">
          <SupplementationFAQ />
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Supplementation;
