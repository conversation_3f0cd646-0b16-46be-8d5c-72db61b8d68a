
import { useState } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { ChevronLeft } from "lucide-react";
import { Link } from "react-router-dom";
import { RodwellMetaTags } from "@/components/calculators/rodwell/RodwellMetaTags";
import { getThemeClasses } from "@/components/ui/theme-utils";

const RodwellCalculator = () => {
  const [selectedCriteria, setSelectedCriteria] = useState<string[]>([]);

  const criteria = [
    {
      id: "leucocitos",
      label: "Leucocitose ou Leucopenia",
      description: "≥ 25.000/mm³ ao nascimento, ≥ 30.000/mm³ 12-24h, ≥ 21.000/mm³ após 48h ou ≤ 5.000/mm³"
    },
    {
      id: "neutrofilos",
      label: "Neutrofilia ou Neutropenia",
      description: "≥ 14.500/mm³ até 60h, ≥ 5.400/mm³ após 60h ou < 7.800/mm³ até 60h, < 1.750/mm³ após 60h"
    },
    {
      id: "neutrofilos_imaturos",
      label: "Elevação de Neutrófilos Imaturos",
      description: "≥ 1.440/mm³ até 60h ou 500-600/mm³ após 60h"
    },
    {
      id: "indice_neutrofilico",
      label: "Índice Neutrofílico Aumentado (PMN imaturos/totais)",
      description: "≥ 0,16 (24h), ≥ 0,13 (24-60h), ≥ 0,12 (>60h)"
    },
    {
      id: "razao_neutrofilos",
      label: "Razão I/T Aumentada",
      description: "Razão dos neutrófilos imaturos/totais ≥ 0,3"
    },
    {
      id: "alteracoes_degenerativas",
      label: "Alterações Degenerativas nos Neutrófilos",
      description: "Vacuolização ou granulação tóxica"
    },
    {
      id: "alteracoes_plaquetas",
      label: "Plaquetopenia",
      description: "≤ 150.000/mm³"
    },
    {
      id: "alteracoes_citoplasmaticas",
      label: "Alterações Citoplasmáticas",
      description: "Corpúsculos de Döhle, granulações tóxicas ou vacuolização"
    }
  ];

  const getResult = (score: number) => {
    if (score >= 5) {
      return {
        text: "Alta probabilidade de sepse neonatal",
        description: "Sepse Provável",
        color: "text-red-600 dark:text-red-400"
      };
    } else if (score >= 3) {
      return {
        text: "Moderada probabilidade de sepse neonatal",
        description: "Sepse Possível",
        color: "text-yellow-600 dark:text-yellow-400"
      };
    }
    return {
      text: "Baixa probabilidade de sepse neonatal",
      description: "Sepse Improvável",
      color: "text-green-600 dark:text-green-400"
    };
  };

  const score = selectedCriteria.length;
  const result = getResult(score);

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col")}>
      <RodwellMetaTags />
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Link to="/calculadoras">
              <Button variant="ghost" size="icon" className="hover:bg-primary/10 dark:hover:bg-primary/20">
                <ChevronLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h1 className={getThemeClasses.gradientHeading("text-3xl")}>
              Escore de Rodwell
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-300">
            Avaliação da probabilidade de sepse neonatal com base em critérios hematológicos
          </p>
          <Card className={getThemeClasses.card("p-6 space-y-6")}>
            {criteria.map((criterion) => (
              <div key={criterion.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50">
                <Checkbox
                  id={criterion.id}
                  checked={selectedCriteria.includes(criterion.id)}
                  onCheckedChange={(checked) => {
                    setSelectedCriteria((prev) =>
                      checked ? [...prev, criterion.id] : prev.filter((id) => id !== criterion.id)
                    );
                  }}
                />
                <div className="space-y-1">
                  <Label htmlFor={criterion.id} className="text-base font-medium cursor-pointer text-gray-800 dark:text-gray-200">
                    {criterion.label}
                  </Label>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{criterion.description}</p>
                </div>
              </div>
            ))}
            <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
              <div className="text-center space-y-4">
                <div className="text-4xl font-bold text-primary dark:text-blue-400">{score} pontos</div>
                <div className={`text-xl font-semibold ${result.color}`}>{result.text}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">{result.description}</div>
              </div>
            </div>
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default RodwellCalculator;
