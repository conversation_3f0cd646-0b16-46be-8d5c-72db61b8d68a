
import React from "react";
import { Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { ElapidicFlowchart } from "@/components/flowcharts/elapidic/ElapidicFlowchart";

const ElapidicFlowchartPage = () => {
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-red-50 via-white to-red-50 dark:from-red-900/20 dark:via-slate-900 dark:to-red-900/10">
      <HelmetWrapper>
        <title>PedBook | Acidente Elapídico</title>
        <meta
          name="description"
          content="Fluxograma para manejo de acidentes elapídicos em pediatria"
        />
      </HelmetWrapper>

      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Link
              to="/flowcharts/venomous"
              className="hidden sm:inline-flex items-center gap-2 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Voltar para Animais Peçonhentos</span>
            </Link>
          </div>

          <div className="text-center space-y-4">
            <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-red-600 to-rose-600 dark:from-red-400 dark:to-rose-400">
              Acidente Elapídico (Coral Verdadeira)
            </h1>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Fluxograma para manejo de acidentes elapídicos em pediatria
            </p>
          </div>

          <ElapidicFlowchart />

          <div className="mt-8 p-6 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">Referência</h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              BRASIL. Ministério da Saúde. Secretaria de Vigilância em Saúde e Ambiente. Departamento de Doenças Transmissíveis. Guia de Animais Peçonhentos do Brasil. Brasília: Ministério da Saúde, 2024.
            </p>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default ElapidicFlowchartPage;
