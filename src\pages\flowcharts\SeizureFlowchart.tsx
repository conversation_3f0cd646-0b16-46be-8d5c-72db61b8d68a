
import React from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowL<PERSON><PERSON>, Brain } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { SeizureFlowContent } from "@/components/flowcharts/seizure/SeizureFlowContent";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { getThemeClasses } from "@/components/ui/theme-utils";

const SeizureFlowchart = () => {
  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col from-yellow-100 via-white to-orange-50 dark:from-yellow-950 dark:via-slate-900 dark:to-orange-950")}>
      <HelmetWrapper>
        <title>PedBook | Crise Convulsiva</title>
        <meta name="description" content="Fluxograma interativo para manejo de crises convulsivas em pediatria" />
      </HelmetWrapper>

      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-8">
          <Link 
            to="/flowcharts" 
            className="inline-flex items-center gap-2 text-orange-600 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Voltar para Fluxogramas</span>
          </Link>

          <div className="text-center space-y-4">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-orange-100 dark:bg-orange-900/30">
              <Brain className="w-8 h-8 text-orange-600 dark:text-orange-400" />
            </div>
            <h1 className={getThemeClasses.gradientHeading("text-4xl font-bold from-orange-600 to-amber-600 dark:from-orange-400 dark:to-amber-400")}>
              Manejo de Crise Convulsiva
            </h1>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Fluxograma interativo para manejo de crises convulsivas em pediatria
            </p>
          </div>

          <SeizureFlowContent />
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default SeizureFlowchart;
