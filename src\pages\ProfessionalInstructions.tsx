
import React, { useState } from 'react';
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { FileText, Search, AlertCircle, Pill, Filter, ArrowLeft } from 'lucide-react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { MedicationsList } from "@/components/medications/MedicationsList";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const ProfessionalInstructions: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const navigate = useNavigate();
  
  
  // Fetch medications that have instructions
  const { data: medications, isLoading: medicationsLoading, error: medicationsError } = useQuery({
    queryKey: ["medications-with-instructions"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_medications")
        .select(`
          id,
          name,
          description,
          brands,
          slug,
          pedbook_medication_categories(id, name),
          pedbook_medication_instructions(id, is_published)
        `)
        .order("name");
      
      if (error) {
        throw error;
      }
      
      // Filter medications that have published instructions
      const medicationsWithInstructions = data.filter(
        med => med.pedbook_medication_instructions && 
               med.pedbook_medication_instructions.length > 0 &&
               med.pedbook_medication_instructions[0]?.is_published
      );

      return medicationsWithInstructions;
    },
  });

  // Fetch all available categories for the filter
  const { data: categories } = useQuery({
    queryKey: ["medication-categories"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_medication_categories")
        .select("id, name")
        .order("name");
      
      if (error) {
        throw error;
      }
      
      return data;
    },
  });

  // Filter medications based on search term and selected category
  const filteredMedications = medications?.filter(medication => {
    const matchesSearch = searchTerm === '' || 
      medication.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      medication.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      medication.brands?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      medication.pedbook_medication_categories?.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || 
      medication.pedbook_medication_categories?.id === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Animation variants for the hero section
  const heroVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-800">
      <HelmetWrapper>
        <title>Bulas Profissionais | PedBook</title>
        <meta 
          name="description" 
          content="Consulte bulas completas de medicamentos para profissionais de saúde no PedBook." 
        />
      </HelmetWrapper>

      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        {/* Hero Section */}
        <motion.div 
          className="max-w-5xl mx-auto mb-10 text-center"
          initial="hidden"
          animate="visible"
          variants={heroVariants}
        >
          <div className="flex items-center justify-center gap-4 mb-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate('/')}
              className="hover:bg-primary/10 hidden sm:flex dark:hover:bg-primary/20"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="inline-flex items-center justify-center gap-2 bg-primary/10 text-primary px-5 py-2 rounded-full text-sm font-medium">
              <Pill className="h-4 w-4" />
              Biblioteca de bulas profissionais
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-700 mb-4">
            Bulas Profissionais
          </h1>
        </motion.div>

        {/* Search and Filter Section */}
        <div className="mb-10">
          <div className="max-w-3xl mx-auto">
            {/* Redesigned Search and Filter Bar */}
            <div className="bg-white dark:bg-slate-800/70 backdrop-blur-sm border border-blue-100 dark:border-blue-900/30 rounded-xl p-4 shadow-lg">
              <div className="flex flex-col md:flex-row gap-4">
                {/* Search Input */}
                <div className="relative flex-1">
                  <div className="absolute left-4 top-1/2 -translate-y-1/2 flex items-center justify-center w-8 h-8 rounded-full bg-blue-50 dark:bg-blue-900/30">
                    <Search className="text-primary h-4 w-4" />
                  </div>
                  <Input
                    type="search"
                    placeholder="Buscar medicamento..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-14 h-12 text-base bg-blue-50/50 dark:bg-slate-700/50 border-blue-100 dark:border-blue-900/30 rounded-lg focus:border-blue-300 dark:focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:focus:ring-blue-800/30"
                  />
                </div>

                {/* Category Filter */}
                <div className="w-full md:w-72">
                  <div className="relative">
                    <div className="absolute left-4 top-1/2 -translate-y-1/2 flex items-center justify-center w-8 h-8 rounded-full bg-blue-50 dark:bg-blue-900/30">
                      <Filter className="text-primary h-4 w-4" />
                    </div>
                    <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                      <SelectTrigger className="pl-14 h-12 text-base bg-blue-50/50 dark:bg-slate-700/50 border-blue-100 dark:border-blue-900/30 rounded-lg focus:border-blue-300 dark:focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:focus:ring-blue-800/30">
                        <SelectValue placeholder="Categoria" />
                      </SelectTrigger>
                      <SelectContent className="bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm border-blue-100 dark:border-blue-900/30 shadow-lg">
                        <SelectGroup>
                          <SelectItem 
                            value="all" 
                            className="focus:bg-blue-50 dark:focus:bg-blue-900/30 rounded-md"
                          >
                            Todas as categorias
                          </SelectItem>
                          {categories?.map((category) => (
                            <SelectItem 
                              key={category.id} 
                              value={category.id}
                              className="focus:bg-blue-50 dark:focus:bg-blue-900/30 rounded-md"
                            >
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-6xl mx-auto">
          {medicationsLoading ? (
            <div className="flex justify-center py-20">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : medicationsError ? (
            <Card className="border-destructive/50 bg-destructive/5 shadow-lg">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-destructive" />
                  Erro ao carregar bulas
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p>Não foi possível carregar as bulas profissionais. Tente novamente mais tarde.</p>
                <Button variant="outline" className="mt-4" onClick={() => window.location.reload()}>
                  Tentar novamente
                </Button>
              </CardContent>
            </Card>
          ) : filteredMedications?.length === 0 ? (
            <motion.div 
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
              className="text-center py-16 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-blue-100"
            >
              <FileText className="h-16 w-16 mx-auto mb-4 text-blue-300" />
              <h2 className="text-2xl font-semibold mb-3 text-gray-800">
                {searchTerm || selectedCategory !== 'all' ? "Nenhum resultado encontrado" : "Nenhuma bula disponível"}
              </h2>
              <p className="text-gray-600 max-w-lg mx-auto mb-6">
                {searchTerm || selectedCategory !== 'all' 
                  ? `Não encontramos medicamentos com os filtros aplicados. Tente diferentes termos ou categorias.` 
                  : "No momento não temos bulas profissionais publicadas. Volte em breve para conferir novos conteúdos."}
              </p>
              {(searchTerm || selectedCategory !== 'all') && (
                <div className="flex flex-wrap justify-center gap-4">
                  {searchTerm && (
                    <Button 
                      variant="outline" 
                      className="mt-4"
                      onClick={() => setSearchTerm('')}
                    >
                      Limpar busca
                    </Button>
                  )}
                  {selectedCategory !== 'all' && (
                    <Button 
                      variant="outline" 
                      className="mt-4"
                      onClick={() => setSelectedCategory('all')}
                    >
                      Remover filtro de categoria
                    </Button>
                  )}
                </div>
              )}
            </motion.div>
          ) : (
            <>
              <div className="flex justify-between items-center mb-6">
                <p className="text-gray-500 dark:text-gray-400">
                  Mostrando {filteredMedications.length} {filteredMedications.length === 1 ? 'medicamento' : 'medicamentos'}
                </p>
                {(searchTerm || selectedCategory !== 'all') && (
                  <Button 
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedCategory('all');
                    }}
                    className="text-sm flex gap-1 items-center hover:bg-red-50 text-red-500 hover:text-red-600"
                  >
                    <AlertCircle className="h-3 w-3" />
                    Limpar filtros
                  </Button>
                )}
              </div>
              <MedicationsList medications={filteredMedications} />
            </>
          )}
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default ProfessionalInstructions;
