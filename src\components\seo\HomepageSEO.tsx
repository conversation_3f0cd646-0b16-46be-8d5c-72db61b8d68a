import HelmetWrapper from "@/components/utils/HelmetWrapper";

interface HomepageSEOProps {
  medicamentosCount?: number;
  categoriasCount?: number;
  usuariosCount?: number;
  popularMedications?: Array<{
    name: string;
    slug: string;
    category: string;
  }>;
  categories?: string[];
}

export const HomepageSEO = ({
  medicamentosCount = 138,
  categoriasCount = 18,
  usuariosCount = 3560,
  popularMedications = [],
  categories = []
}: HomepageSEOProps) => {
  
  // Gerar keywords dinâmicas baseadas nos dados
  const generateDynamicKeywords = () => {
    const baseKeywords = [
      "calculadora pediátrica",
      "doses pediátricas",
      "medicamentos pediátricos",
      "prescrição pediátrica",
      "pediatria",
      "cálculo de dose",
      "posologia pediátrica",
      "farmacologia pediátrica",
      "medicina pediátrica",
      "dosagem infantil"
    ];

    // Adicionar medicamentos populares
    const medicationKeywords = popularMedications.slice(0, 10).map(med => 
      `${med.name.toLowerCase()} pediátrico`
    );

    // Adicionar categorias
    const categoryKeywords = categories.slice(0, 8).map(cat => 
      `${cat.toLowerCase()} pediátrico`
    );

    return [...baseKeywords, ...medicationKeywords, ...categoryKeywords].join(", ");
  };

  // Descrição dinâmica com estatísticas
  const dynamicDescription = `Calculadora pediátrica completa com ${medicamentosCount}+ medicamentos, ${categoriasCount} categorias e mais de ${Math.floor(usuariosCount/1000)}k profissionais cadastrados. Calcule doses, prescrições e acesse fluxogramas clínicos com precisão e segurança.`;

  // Título dinâmico
  const dynamicTitle = `PedBook - ${medicamentosCount}+ Medicamentos Pediátricos | Calculadora de Doses`;

  // Schema.org enriquecido
  const enrichedSchema = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "PedBook",
    "description": dynamicDescription,
    "url": "https://pedb.com.br",
    "applicationCategory": "MedicalApplication",
    "operatingSystem": "Web",
    "author": {
      "@type": "Organization",
      "name": "MedUnity",
      "url": "https://medunity.com.br"
    },
    "offers": {
      "@type": "Offer",
      "category": "Pediatria",
      "price": "0",
      "priceCurrency": "BRL"
    },
    "audience": {
      "@type": "MedicalAudience",
      "audienceType": "Pediatras e profissionais da saúde"
    },
    "featureList": [
      `${medicamentosCount}+ medicamentos pediátricos`,
      `${categoriasCount} categorias de medicamentos`,
      "Cálculo automático de doses",
      "Prescrições digitais",
      "Fluxogramas clínicos",
      "Curvas de crescimento",
      "Calendário vacinal",
      "DNPM (Desenvolvimento Neuropsicomotor)"
    ],
    "applicationSubCategory": "Calculadora Médica",
    "downloadUrl": "https://pedb.com.br",
    "screenshot": "https://pedb.com.br/faviconx.webp",
    "softwareVersion": "2.0",
    "dateModified": new Date().toISOString(),
    "inLanguage": "pt-BR",
    "isAccessibleForFree": true,
    "usageInfo": "Gratuito para profissionais da saúde",
    "specialty": "Pediatria"
  };

  // FAQ Schema para perguntas comuns
  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "Como calcular doses pediátricas?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": `O PedBook oferece calculadora automática para ${medicamentosCount}+ medicamentos pediátricos. Basta inserir peso e idade da criança para obter a dose correta.`
        }
      },
      {
        "@type": "Question",
        "name": "Quantos medicamentos estão disponíveis?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": `Atualmente temos ${medicamentosCount} medicamentos cadastrados em ${categoriasCount} categorias diferentes, todos com cálculos automáticos de dose.`
        }
      },
      {
        "@type": "Question",
        "name": "O PedBook é gratuito?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Sim, o PedBook é completamente gratuito para profissionais da saúde. Já são mais de 3.500 usuários cadastrados."
        }
      }
    ]
  };

  return (
    <HelmetWrapper>
      {/* Título e Descrição Dinâmicos */}
      <title>{dynamicTitle}</title>
      <meta name="description" content={dynamicDescription} />
      <meta name="keywords" content={generateDynamicKeywords()} />

      {/* Meta tags adicionais para SEO */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="bingbot" content="index, follow" />
      
      {/* Geo targeting */}
      <meta name="geo.region" content="BR" />
      <meta name="geo.country" content="Brazil" />
      <meta name="language" content="Portuguese" />

      {/* Open Graph Dinâmico */}
      <meta property="og:title" content={dynamicTitle} />
      <meta property="og:description" content={dynamicDescription} />
      <meta property="og:type" content="website" />
      <meta property="og:url" content="https://pedb.com.br" />
      <meta property="og:image" content="https://pedb.com.br/faviconx.webp" />
      <meta property="og:image:alt" content="PedBook - Calculadora Pediátrica" />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />
      <meta property="og:updated_time" content={new Date().toISOString()} />

      {/* Twitter Cards */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={dynamicTitle} />
      <meta name="twitter:description" content={dynamicDescription} />
      <meta name="twitter:image" content="https://pedb.com.br/faviconx.webp" />
      <meta name="twitter:site" content="@pedbook" />

      {/* Canonical */}
      <link rel="canonical" href="https://pedb.com.br" />

      {/* Schema.org Enriquecido */}
      <script type="application/ld+json">
        {JSON.stringify(enrichedSchema)}
      </script>

      {/* FAQ Schema */}
      <script type="application/ld+json">
        {JSON.stringify(faqSchema)}
      </script>

      {/* Preconnect para performance */}
      <link rel="preconnect" href="https://bxedpdmgvgatjdfxgxij.supabase.co" />
      <link rel="dns-prefetch" href="https://bxedpdmgvgatjdfxgxij.supabase.co" />
    </HelmetWrapper>
  );
};
