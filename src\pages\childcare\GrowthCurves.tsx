
import { useState, useEffect } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { GrowthCurveCard } from "@/components/growth-curves/GrowthCurveCard";
import { GrowthCurveDialog } from "@/components/growth-curves/GrowthCurveDialog";
import { GrowthCurveFilters } from "@/components/growth-curves/GrowthCurveFilters";
import { GrowthCurvePagination } from "@/components/growth-curves/GrowthCurvePagination";
import { GrowthCurveMetaTags } from "@/components/growth-curves/GrowthCurveMetaTags";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useTheme } from "@/context/ThemeContext";

const ITEMS_PER_PAGE = 8;

const GrowthCurves = () => {
  const [selectedCurve, setSelectedCurve] = useState<any>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedGender, setSelectedGender] = useState<string | null>(null);
  const [selectedGestationalAge, setSelectedGestationalAge] = useState<string | null>(null);
  const [selectedGrowthType, setSelectedGrowthType] = useState<string | null>(null);
  const queryClient = useQueryClient();
  const { theme } = useTheme();

  // Usar o tema atual para renderização

  // Prefetch on component mount
  useEffect(() => {
    const prefetchData = async () => {
      await queryClient.prefetchQuery({
        queryKey: ["growth-curves", null, null, null],
        queryFn: fetchGrowthCurves,
        staleTime: 24 * 60 * 60 * 1000, // 24 hours
        gcTime: 48 * 60 * 60 * 1000, // 48 hours
      });
    };
    prefetchData();
  }, [queryClient]);

  const fetchGrowthCurves = async () => {
    let query = supabase
      .from("pedbook_growth_curves")
      .select("*")
      .order("created_at");

    if (selectedGender) {
      query = query.eq("gender", selectedGender);
    }
    if (selectedGestationalAge) {
      query = query.eq("gestational_age", selectedGestationalAge);
    }
    if (selectedGrowthType) {
      query = query.eq("growth_type", selectedGrowthType);
    }

    const { data, error } = await query;
    if (error) throw error;
    return data;
  };

  const { data: curves = [], isLoading } = useQuery({
    queryKey: ["growth-curves", selectedGender, selectedGestationalAge, selectedGrowthType],
    queryFn: fetchGrowthCurves,
    staleTime: 24 * 60 * 60 * 1000, // 24 hours
    gcTime: 48 * 60 * 60 * 1000, // 48 hours
  });

  // Prefetch next page data on hover
  const prefetchNextPage = async () => {
    if (currentPage < totalPages) {
      const nextPageFilters = {
        gender: selectedGender,
        gestationalAge: selectedGestationalAge,
        growthType: selectedGrowthType,
      };
      await queryClient.prefetchQuery({
        queryKey: ["growth-curves", nextPageFilters.gender, nextPageFilters.gestationalAge, nextPageFilters.growthType],
        queryFn: fetchGrowthCurves,
        staleTime: 24 * 60 * 60 * 1000,
      });
    }
  };

  const filteredCurves = curves || [];
  const totalPages = Math.ceil(filteredCurves.length / ITEMS_PER_PAGE);
  const paginatedCurves = filteredCurves.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const handleClearFilters = () => {
    setSelectedGender(null);
    setSelectedGestationalAge(null);
    setSelectedGrowthType(null);
    setCurrentPage(1);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-white via-primary/5 to-white dark:from-slate-900 dark:via-blue-900/5 dark:to-slate-900">
      <GrowthCurveMetaTags />
      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        <Link
          to="/puericultura"
          className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors mb-8 dark:text-blue-400 dark:hover:text-blue-300"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Voltar para Puericultura</span>
        </Link>

        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold mb-4 gradient-text dark:text-blue-300">
            Curvas de Crescimento
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto dark:text-gray-300">
            Consulte as curvas de crescimento padronizadas para acompanhamento do desenvolvimento infantil
          </p>
        </div>

        <div className="space-y-8">
          <GrowthCurveFilters
            selectedGender={selectedGender}
            selectedGestationalAge={selectedGestationalAge}
            selectedGrowthType={selectedGrowthType}
            onGenderChange={setSelectedGender}
            onGestationalAgeChange={setSelectedGestationalAge}
            onGrowthTypeChange={setSelectedGrowthType}
            onClearFilters={handleClearFilters}
          />

          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, index) => (
                <div
                  key={index}
                  className="bg-white dark:bg-slate-800 rounded-xl border border-gray-100 dark:border-gray-700 shadow-md h-64 animate-pulse"
                />
              ))}
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 growth-curves-container no-swipe">
                {paginatedCurves.map((curve) => (
                  <GrowthCurveCard
                    key={curve.id}
                    curve={curve}
                    onViewCurve={(curve) => {
                      setSelectedCurve(curve);
                      setIsDialogOpen(true);
                    }}
                  />
                ))}
              </div>

              <GrowthCurvePagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
            </>
          )}

          <GrowthCurveDialog
            curve={selectedCurve}
            isOpen={isDialogOpen}
            onOpenChange={setIsDialogOpen}
          />
        </div>
      </main>

      <Footer />
    </div>
  );
}

export default GrowthCurves;
