
import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, Lock, ArrowLeft, CheckCircle } from "lucide-react";
import { useNotification } from "@/context/NotificationContext";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

const formSchema = z.object({
  password: z.string().min(6, "A senha deve ter pelo menos 6 caracteres"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "As senhas não coincidem",
  path: ["confirmPassword"],
});

const ResetPassword = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [tokenError, setTokenError] = useState(false);
  const [validatedToken, setValidatedToken] = useState<string | null>(null);
  const [resetSuccess, setResetSuccess] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { showNotification } = useNotification();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  useEffect(() => {
    const validateToken = async () => {
      if (validatedToken) return;

      // console.log("📋 [ResetPassword] Hash recebido:", location.hash);

      try {
        // Extrair o token de acesso do hash da URL
        const hashParams = new URLSearchParams(location.hash.substring(1));
        const token = hashParams.get("access_token");
        const type = hashParams.get("type");

        // console.log("🔑 [ResetPassword] Token extraído:", { token: token ? "presente" : "ausente", type });

        if (!token || type !== "recovery") {
          // console.error("❌ [ResetPassword] Token inválido ou tipo errado:", { token: !!token, type });
          setTokenError(true);
          return;
        }

        try {
          // Verificar se o token é válido obtendo o usuário
          const { data, error } = await supabase.auth.getUser(token);

          if (error) {
            // console.error("❌ [ResetPassword] Erro ao validar token:", error);
            setTokenError(true);
          } else {
            // console.log("✅ [ResetPassword] Token validado com sucesso:", { userId: data?.user?.id });
            setValidatedToken(token);
          }
        } catch (error) {
          // console.error("❌ [ResetPassword] Exceção ao validar token:", error);
          setTokenError(true);
        }
      } catch (error) {
        // console.error("❌ [ResetPassword] Erro no processamento do hash:", error);
        setTokenError(true);
      }
    };

    validateToken();
  }, [location.hash, validatedToken]);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    // console.log("🔄 [ResetPassword] Iniciando atualização de senha");

    if (!validatedToken) {
      // console.error("❌ [ResetPassword] Token não validado");
      showNotification({
        title: "Erro ao atualizar senha",
        description: "Token de redefinição inválido ou expirado.",
        type: "error",
        buttonText: "Voltar"
      });
      return;
    }

    try {
      setIsLoading(true);

      // console.log("📡 [ResetPassword] Enviando solicitação para atualizar senha");

      // Obter a chave anônima (API key) do Supabase para incluir na requisição
      // A URL da API é extraída do cliente Supabase
      const SUPABASE_URL = supabase.auth.url;
      // A chave anônima é extraída do cliente Supabase
      const ANON_KEY = supabase.supabaseKey;

      // console.log("🔑 [ResetPassword] Preparando requisição com URL:", SUPABASE_URL);

      // Utilizando o método de API diretamente em vez do client SDK
      const response = await fetch(`${SUPABASE_URL}/user`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${validatedToken}`,
          'apikey': ANON_KEY
        },
        body: JSON.stringify({
          password: values.password
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        // console.error("❌ [ResetPassword] Erro na resposta da API:", errorData);
        throw new Error(errorData.message || "Erro ao atualizar senha");
      }

      // console.log("✅ [ResetPassword] Senha atualizada com sucesso");

      showNotification({
        title: "Senha atualizada!",
        description: "Sua senha foi atualizada com sucesso.",
        type: "success",
        buttonText: "Ir para o login",
        onButtonClick: () => navigate("/")
      });

      setResetSuccess(true);
    } catch (error: any) {
      // console.error("❌ [ResetPassword] Exceção ao atualizar senha:", error);

      showNotification({
        title: "Erro ao atualizar senha",
        description: error.message || "Ocorreu um erro ao atualizar sua senha. Tente novamente.",
        type: "error",
        buttonText: "Tentar novamente"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const Header = () => (
    <header className="w-full bg-white border-b">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            onClick={() => navigate("/")}
            className="flex items-center gap-2 text-primary hover:text-primary/80"
          >
            <ArrowLeft className="h-4 w-4" />
            Voltar
          </Button>
          <div className="flex items-center">
            <Lock className="h-6 w-6 text-primary mr-2" />
            <span className="font-bold text-lg">PedBook</span>
          </div>
        </div>
      </div>
    </header>
  );

  const Footer = () => (
    <footer className="w-full bg-white border-t mt-auto">
      <div className="container mx-auto px-4 py-6">
        <p className="text-center text-sm text-gray-500">
          © 2025 PedBook. Todos os direitos reservados.
        </p>
      </div>
    </footer>
  );

  if (resetSuccess) {
    return (
      <div className="min-h-screen flex flex-col bg-gray-50">
        <Header />
        <div className="container max-w-md mx-auto px-4 py-12 flex-grow flex items-center justify-center">
          <Card className="w-full shadow-lg border-primary/10">
            <CardContent className="pt-6 pb-8 px-6 flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-6">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h2 className="text-2xl font-bold mb-2">Senha Atualizada!</h2>
              <p className="text-gray-600 mb-6">
                Sua senha foi alterada com sucesso. Você será redirecionado para a página de login em instantes.
              </p>
              <Button
                variant="duolingo"
                className="w-full"
                onClick={() => navigate("/")}
              >
                Ir para o login
              </Button>
            </CardContent>
          </Card>
        </div>
        <Footer />
      </div>
    );
  }

  if (tokenError) {
    return (
      <div className="min-h-screen flex flex-col bg-gray-50">
        <Header />
        <div className="container max-w-md mx-auto px-4 py-12 flex-grow flex items-center">
          <Card className="w-full shadow-lg border-red-200">
            <CardHeader className="pb-4">
              <div className="flex justify-center mb-4">
                <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
                  <AlertCircle className="h-6 w-6 text-red-600" />
                </div>
              </div>
              <CardTitle className="text-center text-xl">Link inválido ou expirado</CardTitle>
            </CardHeader>
            <CardContent className="pt-2">
              <p className="text-center text-gray-600 mb-6">
                O link de redefinição de senha é inválido ou expirou.
                Por favor, solicite uma nova redefinição de senha.
              </p>
              <Button
                variant="duolingo"
                onClick={() => navigate("/")}
                className="w-full"
              >
                Voltar para o início
              </Button>
            </CardContent>
          </Card>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Header />
      <div className="container max-w-md mx-auto px-4 py-12 flex-grow flex items-center">
        <Card className="w-full shadow-lg border-primary/10">
          <CardHeader className="space-y-1">
            <div className="flex justify-center mb-4">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                <Lock className="h-6 w-6 text-primary" />
              </div>
            </div>
            <CardTitle className="text-center text-xl">Redefinir Senha</CardTitle>
            <CardDescription className="text-center">Crie uma nova senha segura para sua conta</CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nova Senha</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="Digite sua nova senha"
                          className="bg-gray-50 border-gray-200 focus:border-primary"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirmar Nova Senha</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="Confirme sua nova senha"
                          className="bg-gray-50 border-gray-200 focus:border-primary"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button
                  type="submit"
                  variant="duolingo"
                  className="w-full"
                  disabled={isLoading}
                >
                  {isLoading ? "Atualizando..." : "Atualizar Senha"}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
      <Footer />
    </div>
  );
};

export default ResetPassword;
