
import React from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import PersonalInfoForm from "./PersonalInfoForm";
import PasswordForm from "./PasswordForm";

interface ProfileFormProps {
  profile: any;
  setProfile: (profile: any) => void;
  handleProfileUpdate: (e: React.FormEvent<HTMLFormElement>) => void;
}

const ProfileForm = ({ profile, setProfile, handleProfileUpdate }: ProfileFormProps) => {
  return (
    <Tabs defaultValue="personal" className="space-y-6">
      <TabsList className="grid w-full grid-cols-2 gap-2 bg-gray-100 dark:bg-slate-800 p-1">
        <TabsTrigger 
          value="personal" 
          className="w-full data-[state=active]:bg-primary data-[state=active]:text-white border border-input dark:border-slate-600 rounded-full dark:text-gray-300 dark:data-[state=inactive]:bg-slate-700"
        >
          In<PERSON><PERSON><PERSON><PERSON><PERSON>
        </TabsTrigger>
        <TabsTrigger 
          value="password" 
          className="w-full data-[state=active]:bg-primary data-[state=active]:text-white border border-input dark:border-slate-600 rounded-full dark:text-gray-300 dark:data-[state=inactive]:bg-slate-700"
        >
          Trocar Senha
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="personal">
        <PersonalInfoForm
          profile={profile}
          setProfile={setProfile}
          handleProfileUpdate={handleProfileUpdate}
        />
      </TabsContent>
      
      <TabsContent value="password">
        <PasswordForm />
      </TabsContent>
    </Tabs>
  );
};

export default ProfileForm;
