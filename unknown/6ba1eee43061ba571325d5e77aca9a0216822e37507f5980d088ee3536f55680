/**
 * Script simplificado para testar dados reais do Supabase
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

console.log('🚀 Testando dados reais do Supabase...');

const supabaseUrl = 'https://bxedpdmgvgatjdfxgxij.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ4ZWRwZG1ndmdhdGpkZnhneGlqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzIyNzk3MTgsImV4cCI6MjA0Nzg1NTcxOH0.cjoaggOXt1kY9WmVNbAipCOQ2dP4PWLP43KMf8cO8Wo';
const supabase = createClient(supabaseUrl, supabaseKey);

async function fetchMedicationsData() {
  try {
    console.log('🔍 Buscando TODOS os medicamentos do banco de dados...');

    const { data, error } = await supabase
      .from('pedbook_medications')
      .select(`
        id,
        slug,
        name,
        brands,
        contraindications,
        guidelines,
        scientific_references,
        pedbook_medication_categories (
          name
        ),
        pedbook_medication_dosages (
          name,
          summary,
          dosage_template
        )
      `)
      .order('name', { ascending: true }); // Buscar TODOS os medicamentos ordenados alfabeticamente

    if (error) {
      console.error('❌ Erro:', error);
      return [];
    }

    if (data && data.length > 0) {
      console.log(`✅ Encontrados ${data.length} medicamentos:`);
      data.forEach(med => {
        console.log(`📋 ${med.name} (${med.slug})`);
      });
      return data;
    } else {
      console.log('⚠️ Nenhum medicamento encontrado');
      return [];
    }

  } catch (err) {
    console.error('❌ Erro na conexão:', err.message);
    return null;
  }
}

async function generateMedicationPages(medicationsData) {
  if (!medicationsData || medicationsData.length === 0) {
    console.log('❌ Sem dados para gerar páginas');
    return;
  }

  console.log(`\n📄 Gerando ${medicationsData.length} páginas HTML...`);

  const distPath = path.join(process.cwd(), 'dist');

  // Verificar se dist existe
  if (!fs.existsSync(distPath)) {
    console.log('❌ Diretório dist/ não encontrado');
    return;
  }

  // Verificar se index.html existe
  const indexPath = path.join(distPath, 'index.html');
  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html não encontrado');
    return;
  }

  // Ler HTML base uma vez
  const baseHtml = fs.readFileSync(indexPath, 'utf8');

  let successCount = 0;

  // Processar cada medicamento
  for (const medicationData of medicationsData) {
    try {
      // Criar diretório do medicamento
      const medDir = path.join(distPath, 'medicamentos', medicationData.slug);
      if (!fs.existsSync(medDir)) {
        fs.mkdirSync(medDir, { recursive: true });
      }

      // Gerar conteúdo personalizado
      const title = `${medicationData.name} - Dose Pediátrica | PedBook`;

      // Combinar name + summary das dosagens
      const dosageInfo = medicationData.pedbook_medication_dosages?.map(d => {
        const name = d.name || '';
        const summary = d.summary || '';
        const cleanSummary = summary.replace(/\n/g, ' ').replace(/\r/g, ' ').replace(/\s+/g, ' ').trim();
        return name && cleanSummary ? `${name}: ${cleanSummary}` : name || cleanSummary;
      }).filter(Boolean) || [];

      const mainDosageInfo = dosageInfo[0] || 'Consulte bula';
      const allDosageForms = medicationData.pedbook_medication_dosages?.map(d => d.name).filter(Boolean) || [];

      const indication = medicationData.guidelines ?
        medicationData.guidelines.split('\n')[0].replace(/^- /, '').substring(0, 80) :
        'Medicamento pediátrico';

      // Limpar indicação
      const cleanIndication = indication.replace(/\n/g, ' ').replace(/\r/g, ' ').replace(/\s+/g, ' ').trim();

      const description = `${medicationData.name}: ${mainDosageInfo}. ${cleanIndication}. Calculadora automática, posologia e indicações para pediatria.`;

      // Função para normalizar texto (remover acentos e caracteres especiais)
      function normalizeText(text) {
        return text
          .toLowerCase()
          .normalize('NFD')
          .replace(/[\u0300-\u036f]/g, '') // Remove acentos
          .replace(/[^\w\s]/g, ' ') // Remove caracteres especiais
          .replace(/\s+/g, ' ') // Remove espaços duplos
          .trim();
      }

      // Keywords mais específicas incluindo apresentações
      const dosageKeywords = allDosageForms.map(form =>
        `${normalizeText(medicationData.name)} ${normalizeText(form)}`
      ).slice(0, 3).join(', '); // Máximo 3 para não ficar muito longo

      const medName = normalizeText(medicationData.name);
      const keywords = `${medName}, dose ${medName}, ${medName} pediatrico, posologia ${medName}, ${dosageKeywords}, dose pediatrica, calculadora dose, pediatria`;
      const categoryName = medicationData.pedbook_medication_categories?.name || 'Medicamentos';

      let customHtml = baseHtml;

      // Substituir título
      customHtml = customHtml.replace(
        /<title>.*?<\/title>/,
        `<title>${title}</title>`
      );

      // Substituir meta description
      customHtml = customHtml.replace(
        /<meta name="description" content=".*?" ?\/?>/,
        `<meta name="description" content="${description}" />`
      );

      // Substituir meta keywords
      customHtml = customHtml.replace(
        /<meta name="keywords" content=".*?" ?\/?>/,
        `<meta name="keywords" content="${keywords}" />`
      );

      // Atualizar canonical URL
      customHtml = customHtml.replace(
        /<link rel="canonical" href=".*?" \/>/,
        `<link rel="canonical" href="https://pedb.com.br/medicamentos/${medicationData.slug}" />`
      );

      // Atualizar Open Graph tags
      customHtml = customHtml.replace(
        /<meta property="og:title" content=".*?" ?\/?>/,
        `<meta property="og:title" content="${title}" />`
      );

      customHtml = customHtml.replace(
        /<meta property="og:description" content=".*?" ?\/?>/,
        `<meta property="og:description" content="${description}" />`
      );

      customHtml = customHtml.replace(
        /<meta property="og:url" content=".*?" ?\/?>/,
        `<meta property="og:url" content="https://pedb.com.br/medicamentos/${medicationData.slug}" />`
      );

      // Atualizar Twitter Cards
      customHtml = customHtml.replace(
        /<meta name="twitter:title" content=".*?" ?\/?>/,
        `<meta name="twitter:title" content="${title}" />`
      );

      customHtml = customHtml.replace(
        /<meta name="twitter:description" content=".*?" ?\/?>/,
        `<meta name="twitter:description" content="${description}" />`
      );

      customHtml = customHtml.replace(
        /<meta name="twitter:url" content=".*?" ?\/?>/,
        `<meta name="twitter:url" content="https://pedb.com.br/medicamentos/${medicationData.slug}" />`
      );

      // Adicionar conteúdo SEO
      const dosagesSeoContent = dosageInfo.map((info, index) =>
        `<h3>Apresentação ${index + 1}</h3><p>${info}</p>`
      ).join('\n      ');

      const seoContent = `
    <div id="root"></div>
    <!-- SEO Content for Crawlers -->
    <div style="display: none;" data-seo-content>
      <h1>${medicationData.name} - Dose Pediátrica</h1>
      <p>Informações completas sobre ${medicationData.name} em pediatria, incluindo doses, apresentações e posologia.</p>

      <h2>Nomes Comerciais</h2>
      <p>${medicationData.brands || 'Diversos laboratórios farmacêuticos'}</p>

      <h2>Apresentações e Doses Pediátricas</h2>
      ${dosagesSeoContent || '<p>Consulte bula para dosagem</p>'}

      <h2>Categoria</h2>
      <p>${categoryName}</p>

      ${medicationData.contraindications ? `
      <h2>Contra-indicações</h2>
      <p>${medicationData.contraindications.replace(/\n/g, '<br>')}</p>
      ` : ''}

      ${medicationData.guidelines ? `
      <h2>Orientações</h2>
      <p>${medicationData.guidelines.replace(/\n/g, '<br>')}</p>
      ` : ''}

      <h2>Calculadora de Dose</h2>
      <p>Use nossa calculadora automática para determinar a dose correta de ${medicationData.name} baseada no peso da criança.</p>

      ${medicationData.scientific_references ? `
      <h2>Referências Científicas</h2>
      <p>${medicationData.scientific_references.replace(/\n/g, '<br>')}</p>
      ` : ''}
    </div>`;

      customHtml = customHtml.replace('<div id="root"></div>', seoContent);

      // Adicionar structured data específico para medicamentos
      const dosageForms = allDosageForms.length > 0 ? allDosageForms : ["Suspensão oral", "Comprimido", "Solução"];
      const availableStrengths = medicationData.pedbook_medication_dosages?.map(d => {
        const match = d.name?.match(/(\d+(?:\.\d+)?)\s*(mg|g|mL)/);
        return match ? `${match[1]} ${match[2]}` : null;
      }).filter(Boolean) || [];

      // Rich Snippets - Drug Schema avançado
      const drugSchema = {
        "@context": "https://schema.org",
        "@type": "Drug",
        "name": medicationData.name,
        "description": description,
        "url": `https://pedb.com.br/medicamentos/${medicationData.slug}`,
        "image": "https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/faviconx.webp",
        "activeIngredient": medicationData.name,
        "dosageForm": dosageForms,
        "availableStrength": availableStrengths.length > 0 ? availableStrengths : undefined,
        "manufacturer": {
          "@type": "Organization",
          "name": "Diversos laboratórios farmacêuticos"
        },
        "category": categoryName,
        "brand": medicationData.brands || "Diversos laboratórios",
        "audience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde Pediátrica"
        },
        "offers": {
          "@type": "Offer",
          "availability": "https://schema.org/InStock",
          "price": "0",
          "priceCurrency": "BRL",
          "description": "Informações gratuitas sobre dosagem pediátrica",
          "seller": {
            "@type": "Organization",
            "name": "PedBook"
          }
        }
      };

      // FAQ Schema para Rich Snippets
      const faqSchema = {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": [
          {
            "@type": "Question",
            "name": `Qual a dose de ${medicationData.name} para crianças?`,
            "acceptedAnswer": {
              "@type": "Answer",
              "text": `A dose de ${medicationData.name} varia conforme o peso e idade da criança. Consulte sempre um pediatra e use nossa calculadora como ferramenta de apoio para verificar a dosagem adequada.`
            }
          },
          {
            "@type": "Question",
            "name": `Como administrar ${medicationData.name} em pediatria?`,
            "acceptedAnswer": {
              "@type": "Answer",
              "text": `${medicationData.name} deve ser administrado conforme prescrição médica. Verifique sempre a concentração do medicamento e use dispositivos de medição adequados para garantir a dose correta.`
            }
          },
          {
            "@type": "Question",
            "name": `Quais as apresentações disponíveis de ${medicationData.name}?`,
            "acceptedAnswer": {
              "@type": "Answer",
              "text": `${medicationData.name} está disponível em diferentes apresentações e concentrações. Consulte nossa página para ver todas as opções e suas respectivas dosagens pediátricas.`
            }
          }
        ]
      };

      // Dosage Schema para informações de posologia
      const dosageSchema = {
        "@context": "https://schema.org",
        "@type": "MedicalGuideline",
        "name": `Posologia de ${medicationData.name} em Pediatria`,
        "description": `Diretrizes para dosagem de ${medicationData.name} em crianças e adolescentes`,
        "medicalSpecialty": "Pediatria",
        "guidelineSubject": {
          "@type": "Drug",
          "name": medicationData.name
        },
        "audience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde Pediátrica"
        }
      };

      // Breadcrumbs Schema
      const breadcrumbsSchema = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "PedBook",
            "item": "https://pedb.com.br"
          },
          {
            "@type": "ListItem",
            "position": 2,
            "name": "Medicamentos",
            "item": "https://pedb.com.br/medicamentos/painel"
          },
          {
            "@type": "ListItem",
            "position": 3,
            "name": medicationData.name,
            "item": `https://pedb.com.br/medicamentos/${medicationData.slug}`
          }
        ]
      };

      // Gerar JSON-LD para todos os schemas
      const drugJsonLd = `<script type="application/ld+json">${JSON.stringify(drugSchema, null, 2)}</script>`;
      const faqJsonLd = `<script type="application/ld+json">${JSON.stringify(faqSchema, null, 2)}</script>`;
      const dosageJsonLd = `<script type="application/ld+json">${JSON.stringify(dosageSchema, null, 2)}</script>`;
      const breadcrumbsJsonLd = `<script type="application/ld+json">${JSON.stringify(breadcrumbsSchema, null, 2)}</script>`;

      customHtml = customHtml.replace('</head>', `  ${drugJsonLd}\n  ${faqJsonLd}\n  ${dosageJsonLd}\n  ${breadcrumbsJsonLd}\n</head>`);

      // Salvar arquivo
      fs.writeFileSync(path.join(medDir, 'index.html'), customHtml);
      console.log(`✅ Criado: /medicamentos/${medicationData.slug}/index.html`);
      successCount++;

    } catch (err) {
      console.error(`❌ Erro ao gerar ${medicationData.slug}:`, err.message);
    }
  }

  console.log(`\n🎉 Geradas ${successCount}/${medicationsData.length} páginas com sucesso!`);
  return { successCount, medicationsData };
}

/**
 * Buscar condutas para o sitemap
 */
async function fetchConductsForSitemap() {
  try {
    const { data, error } = await supabase
      .from('pedbook_conducts_summaries')
      .select(`
        slug,
        pedbook_conducts_topics (
          pedbook_conducts_categories (
            slug
          )
        )
      `)
      .eq('published', true);

    if (error) {
      console.error('❌ Erro ao buscar condutas para sitemap:', error);
      return [];
    }

    // Retornar com categoria para URL correta
    return data?.map(conduct => ({
      slug: conduct.slug,
      categorySlug: conduct.pedbook_conducts_topics?.pedbook_conducts_categories?.slug
    })).filter(c => c.categorySlug) || [];
  } catch (err) {
    console.error('❌ Erro na conexão para condutas:', err.message);
    return [];
  }
}

/**
 * Gerar sitemap.xml automático
 */
async function generateSitemap(medicationsData) {
  console.log('\n🗺️ Gerando sitemap.xml completo...');

  const distPath = path.join(process.cwd(), 'dist');
  const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD

  // Buscar condutas para o sitemap
  const conductsData = await fetchConductsForSitemap();

  // URLs estáticas
  const staticUrls = [
    {
      url: 'https://pedb.com.br/',
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: '1.0'
    },
    {
      url: 'https://pedb.com.br/medicamentos/painel',
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: '0.9'
    },
    {
      url: 'https://pedb.com.br/condutas-e-manejos',
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: '0.9'
    },
    {
      url: 'https://pedb.com.br/calculadoras',
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: '0.8'
    }
  ];

  // URLs dos medicamentos
  const medicationUrls = medicationsData.map(med => ({
    url: `https://pedb.com.br/medicamentos/${med.slug}`,
    lastmod: currentDate,
    changefreq: 'monthly',
    priority: '0.8'
  }));

  // URLs das condutas
  const conductUrls = conductsData.map(conduct => ({
    url: `https://pedb.com.br/condutas-e-manejos/${conduct.categorySlug}/${conduct.slug}`,
    lastmod: currentDate,
    changefreq: 'monthly',
    priority: '0.8'
  }));

  // Combinar todas as URLs
  const allUrls = [...staticUrls, ...medicationUrls, ...conductUrls];

  // Gerar XML
  const sitemapXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allUrls.map(url => `  <url>
    <loc>${url.url}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
  </url>`).join('\n')}
</urlset>`;

  // Salvar sitemap
  fs.writeFileSync(path.join(distPath, 'sitemap.xml'), sitemapXml);
  console.log(`✅ Sitemap completo gerado:`);
  console.log(`   📄 ${medicationUrls.length} medicamentos`);
  console.log(`   📋 ${conductUrls.length} condutas`);
  console.log(`   🏠 ${staticUrls.length} páginas estáticas`);
  console.log(`   🎯 Total: ${allUrls.length} URLs`);

  return allUrls.length;
}

// Executar teste
async function main() {
  const medicationsData = await fetchMedicationsData();
  if (medicationsData && medicationsData.length > 0) {
    const result = await generateMedicationPages(medicationsData);
    const sitemapUrls = await generateSitemap(result.medicationsData);
    console.log(`\n🎉 Processo concluído!`);
    console.log(`📄 ${result.successCount} páginas geradas com dados reais do Supabase!`);
    console.log(`🗺️ Sitemap gerado com ${sitemapUrls} URLs!`);
  } else {
    console.log('\n❌ Nenhum medicamento encontrado');
  }
}

main().catch(console.error);
