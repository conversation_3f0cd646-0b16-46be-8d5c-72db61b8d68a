
import React, { useRef, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Lock } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";

const PasswordForm = () => {
  const { toast } = useToast();
  const formRef = useRef<HTMLFormElement>(null);
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const handlePasswordChange = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    
    const formData = new FormData(e.currentTarget);
    const currentPassword = formData.get('current_password') as string;
    const newPassword = formData.get('new_password') as string;
    const confirmPassword = formData.get('confirm_password') as string;

    if (newPassword !== confirmPassword) {
      toast({
        variant: "destructive",
        title: "Erro",
        description: "As senhas não coincidem",
      });
      setIsLoading(false);
      return;
    }

    try {
      console.log("🔄 [PasswordForm] Iniciando atualização de senha");
      
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) {
        console.error("❌ [PasswordForm] Erro ao atualizar senha:", error);
        throw error;
      }

      console.log("✅ [PasswordForm] Senha atualizada com sucesso");
      
      toast({
        title: "Sucesso",
        description: "Senha atualizada com sucesso",
      });

      formRef.current?.reset();
    } catch (error: any) {
      console.error("❌ [PasswordForm] Exceção ao atualizar senha:", error);
      
      toast({
        variant: "destructive",
        title: "Erro ao atualizar senha",
        description: error.message || "Ocorreu um erro ao atualizar a senha",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="bg-white dark:bg-slate-800 border dark:border-slate-700">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 dark:text-gray-100">
          <Lock className="h-5 w-5" />
          Alterar Senha
        </CardTitle>
        <CardDescription className="dark:text-gray-400">
          Atualize sua senha de acesso aqui.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form ref={formRef} onSubmit={handlePasswordChange} className="space-y-4">
          {/* Campo de usuário oculto para acessibilidade */}
          <Input
            type="text"
            id="username"
            name="username"
            autoComplete="username"
            defaultValue={user?.email || ''}
            className="hidden"
            aria-hidden="true"
          />
          
          <div className="space-y-2">
            <Label htmlFor="current_password" className="dark:text-gray-300">Senha atual</Label>
            <Input
              id="current_password"
              name="current_password"
              type="password"
              autoComplete="current-password"
              className="bg-white dark:bg-slate-700 dark:border-slate-600 dark:text-gray-200"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="new_password" className="dark:text-gray-300">Nova senha</Label>
            <Input
              id="new_password"
              name="new_password"
              type="password"
              autoComplete="new-password"
              className="bg-white dark:bg-slate-700 dark:border-slate-600 dark:text-gray-200"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="confirm_password" className="dark:text-gray-300">Confirme a nova senha</Label>
            <Input
              id="confirm_password"
              name="confirm_password"
              type="password"
              autoComplete="new-password"
              className="bg-white dark:bg-slate-700 dark:border-slate-600 dark:text-gray-200"
              required
            />
          </div>

          <Button 
            type="submit" 
            variant="outline" 
            className="w-full sm:w-auto dark:bg-slate-700 dark:hover:bg-slate-600 dark:text-gray-200 dark:border-slate-600"
            disabled={isLoading}
          >
            {isLoading ? "Atualizando..." : "Atualizar senha"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default PasswordForm;
