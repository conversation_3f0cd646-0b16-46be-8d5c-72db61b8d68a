
import { Shield, MessageSquare } from "lucide-react";
import { <PERSON><PERSON> } from "../ui/button";
import { useNavigate } from "react-router-dom";
import { Badge } from "../ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import md5 from "md5";
import type { User } from "@supabase/supabase-js";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import { useState } from "react";
import { FeedbackPage } from "@/pages/feedback/FeedbackPage";
import { useNotification } from "@/context/NotificationContext";

interface HeaderActionsProps {
  user: User | null;
  profile: any;
  isAdmin: boolean;
  onFeedbackClick: () => void;
  onLogout: () => void;
}

export const HeaderActions = ({
  user,
  profile,
  isAdmin,
  onFeedbackClick,
  onLogout
}: HeaderActionsProps) => {
  const navigate = useNavigate();
  const [showFeedbackDialog, setShowFeedbackDialog] = useState(false);
  const { showNotification } = useNotification();

  const getGravatarUrl = (email: string) => {
    const hash = md5(email.toLowerCase().trim());
    return `https://www.gravatar.com/avatar/${hash}?d=mp`;
  };

  const handleLogout = async () => {
    onLogout();
    showNotification({
      title: "Logout realizado com sucesso",
      description: "Você foi desconectado da sua conta.",
      type: "success",
      buttonText: "Continuar",
      onButtonClick: () => navigate("/")
    });
  };

  const handleFeedbackClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowFeedbackDialog(true);
    // Still call the prop callback in case parent components need to know
    onFeedbackClick();
  };

  return (
    <div className="flex items-center gap-2 shrink-0">
      {isAdmin && (
        <Button
          variant="outline"
          className="flex items-center gap-2 bg-gradient-to-r from-primary/10 to-transparent hover:from-primary/20"
          onClick={() => {
            console.log("🔘 HeaderActions - Botão admin clicado, navegando para /admin/dashboard");
            navigate("/admin/dashboard");
          }}
        >
          <Shield className="h-4 w-4" />
          <span className="hidden sm:inline">Painel Admin</span>
          <Badge variant="secondary" className="ml-2 bg-primary/20">
            Admin
          </Badge>
        </Button>
      )}

      {user && (
        <DropdownMenu>
          <DropdownMenuTrigger className="focus:outline-none">
            <div className="flex items-center gap-2 p-1.5 rounded-full bg-gradient-to-r from-primary/10 to-transparent hover:from-primary/20 transition-colors">
              <Avatar>
                <AvatarImage
                  src={profile?.avatar_url || getGravatarUrl(user.email || '')}
                  alt={profile?.full_name || 'User'}
                />
                <AvatarFallback>
                  {(profile?.full_name || 'U').charAt(0)}
                </AvatarFallback>
              </Avatar>
              <span className="hidden sm:block text-sm font-medium">
                {profile?.full_name}
              </span>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56 bg-white border border-primary/20 dark:bg-slate-800 dark:border-slate-700">
            <DropdownMenuLabel className="dark:text-gray-300">Minha Conta</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => navigate('/settings')} className="dark:text-gray-300 dark:focus:bg-slate-700 dark:focus:text-white">
              Configurações
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleFeedbackClick} className="dark:text-gray-300 dark:focus:bg-slate-700 dark:focus:text-white">
              <MessageSquare className="h-4 w-4 mr-2" />
              Feedback/Sugestões
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleLogout} className="dark:text-gray-300 dark:focus:bg-slate-700 dark:focus:text-white">
              Sair
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {/* Feedback Dialog */}
      {user && (
        <Dialog
          open={showFeedbackDialog}
          onOpenChange={(value) => {
            setShowFeedbackDialog(value);
            // Ensure pointer events are re-enabled when dialog closes
            if (!value) {
              document.body.style.pointerEvents = 'auto';
            }
          }}
        >
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto dark:bg-slate-800">
            <DialogHeader>
              <DialogTitle>Feedback</DialogTitle>
            </DialogHeader>
            <FeedbackPage />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
