import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import {
  Play,
  ArrowRight,
  Spark<PERSON>,
  Heart,
  Stethoscope,
  BookOpen,
  Target,
  Clock,
  Trophy,
  CheckCircle,
  Star,
  Zap,
  Brain,
  TrendingUp,
  HelpCircle
} from "lucide-react";
import { Button } from "@/components/ui/button";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { PedBookTutorial } from "@/components/tutorial/PedBookTutorial";

const PediatricStudyIntro = () => {
  const navigate = useNavigate();
  const [showTutorial, setShowTutorial] = useState(false);

  const handleOpenTutorial = () => {
    setShowTutorial(true);
  };

  const handleCloseTutorial = () => {
    setShowTutorial(false);
  };

  const handleStartStudy = () => {
    navigate('/estudos/filtros');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
      <HelmetWrapper>
        <title>PedBook - Estudos de Pediatria</title>
        <meta name="description" content="Estude pediatria para residência médica através do PedBook. Questões do MedEvo focadas em pediatria." />
      </HelmetWrapper>

      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute -top-40 -right-40 w-80 h-80 bg-blue-200/30 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-200/30 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      <Header />

      <main className="relative z-10">
        {/* Hero Section - Mais compacto */}
        <div className="pt-16 pb-12 px-4">
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-8"
            >
              <motion.div
                className="inline-flex items-center gap-3 mb-6"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <motion.div
                  className="p-3 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <Heart className="h-8 w-8 text-pink-500" />
                </motion.div>
                <motion.div
                  className="p-3 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg"
                  whileHover={{ scale: 1.1, rotate: -5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <Stethoscope className="h-8 w-8 text-blue-500" />
                </motion.div>
              </motion.div>

              <motion.h1
                className="text-4xl md:text-6xl font-bold mb-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                <span className="text-gray-800">Ped</span>
                <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-transparent bg-clip-text">
                  Book
                </span>
              </motion.h1>

              <motion.p
                className="text-lg md:text-xl text-gray-600 mb-4 leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                Estude <span className="font-semibold text-blue-600">pediatria</span> para residência médica
              </motion.p>

              <motion.div
                className="flex flex-wrap justify-center gap-2 mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.7 }}
              >
                <span className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium">
                  Questões do MedEvo
                </span>
                <span className="bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium">
                  Filtros avançados
                </span>
                <span className="bg-pink-100 text-pink-700 px-3 py-1 rounded-full text-sm font-medium">
                  Mix personalizado
                </span>
              </motion.div>
            </motion.div>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  onClick={() => navigate('/estudos/filtros')}
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-10 py-4 text-lg rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 border-0"
                >
                  <Play className="h-5 w-5 mr-3" />
                  Começar Estudos
                  <ArrowRight className="h-5 w-5 ml-3" />
                </Button>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  variant="outline"
                  onClick={() => window.open('https://medevo.com.br', '_blank')}
                  size="lg"
                  className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-purple-300 px-10 py-4 text-lg rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <BookOpen className="h-5 w-5 mr-3" />
                  MedEvo Completo
                </Button>
              </motion.div>
            </motion.div>
          </div>
        </div>

        {/* Value Proposition - Mais direto */}
        <div className="px-4 pb-12">
          <motion.div
            className="max-w-5xl mx-auto"
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
          >
            <div className="text-center mb-10">
              <motion.div
                className="inline-flex items-center gap-2 bg-gradient-to-r from-yellow-100 to-orange-100 px-4 py-2 rounded-full mb-4"
                whileHover={{ scale: 1.05 }}
              >
                <Trophy className="h-4 w-4 text-orange-600" />
                <span className="text-sm font-semibold text-orange-800">Ferramenta Premium Gratuita</span>
              </motion.div>

              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-3">
                Por que o PedBook é <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">diferente?</span>
              </h2>

              <p className="text-gray-600 max-w-2xl mx-auto">
                Teste a experiência do MedEvo focada em pediatria. Uma prévia da plataforma completa de estudos para residência médica.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-6">
              <motion.div
                className="group text-center p-6 rounded-2xl bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 cursor-pointer"
                whileHover={{ scale: 1.05, y: -8 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div
                  className="bg-blue-600 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-700 transition-colors"
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.6 }}
                >
                  <Target className="h-6 w-6 text-white" />
                </motion.div>
                <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-blue-700 transition-colors">Foco Especializado</h3>
                <p className="text-sm text-gray-600 group-hover:text-gray-700 transition-colors">Questões selecionadas especificamente para pediatria, sem distrações</p>
                <div className="mt-3 opacity-0 group-hover:opacity-100 transition-opacity">
                  <span className="text-xs text-blue-600 font-medium">✨ Apenas pediatria</span>
                </div>
              </motion.div>

              <motion.div
                className="group text-center p-6 rounded-2xl bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 cursor-pointer"
                whileHover={{ scale: 1.05, y: -8 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div
                  className="bg-purple-600 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-700 transition-colors"
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.6 }}
                >
                  <Brain className="h-6 w-6 text-white" />
                </motion.div>
                <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-purple-700 transition-colors">Estudo Inteligente</h3>
                <p className="text-sm text-gray-600 group-hover:text-gray-700 transition-colors">Filtros avançados e mix personalizado para maximizar seu aprendizado</p>
                <div className="mt-3 opacity-0 group-hover:opacity-100 transition-opacity">
                  <span className="text-xs text-purple-600 font-medium">🧠 IA integrada</span>
                </div>
              </motion.div>

              <motion.div
                className="group text-center p-6 rounded-2xl bg-gradient-to-br from-green-50 to-green-100 border border-green-200 cursor-pointer"
                whileHover={{ scale: 1.05, y: -8 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div
                  className="bg-green-600 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:bg-green-700 transition-colors"
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.6 }}
                >
                  <TrendingUp className="h-6 w-6 text-white" />
                </motion.div>
                <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-green-700 transition-colors">Plataforma Confiável</h3>
                <p className="text-sm text-gray-600 group-hover:text-gray-700 transition-colors">Baseado no MedEvo, plataforma consolidada de preparação para residência médica</p>
                <div className="mt-3 opacity-0 group-hover:opacity-100 transition-opacity">
                  <span className="text-xs text-green-600 font-medium">🚀 Tecnologia MedEvo</span>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>

        {/* Features Showcase - Mais compacto */}
        <div className="px-4 pb-12">
          <motion.div
            className="max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
          >
            <div className="text-center mb-8">
              <motion.div
                className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-100 to-purple-100 px-4 py-2 rounded-full mb-4"
                whileHover={{ scale: 1.05 }}
              >
                <Sparkles className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-gray-700">Teste as ferramentas do MedEvo</span>
              </motion.div>

              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-3">
                Experimente os <span className="text-blue-600">recursos premium</span>
              </h2>
              <p className="text-gray-600 mb-4">
                Uma prévia das ferramentas que aceleram sua aprovação na residência
              </p>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  onClick={handleOpenTutorial}
                  variant="outline"
                  className="bg-white/80 border-2 border-blue-200 hover:border-blue-300 px-6 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <HelpCircle className="h-4 w-4 mr-2" />
                  Ver Tutorial Interativo
                </Button>
              </motion.div>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <motion.div
                className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50"
                whileHover={{ y: -5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="flex items-start gap-4">
                  <div className="bg-blue-100 p-3 rounded-xl">
                    <Clock className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Filtros Inteligentes</h3>
                    <p className="text-gray-600 text-sm">
                      Filtre por especialidade, tema, foco, instituição e ano.
                      Estude exatamente o que precisa para sua prova.
                    </p>
                    <div className="flex items-center gap-2 mt-3">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-xs text-gray-500">Filtros do MedEvo</span>
                    </div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50"
                whileHover={{ y: -5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="flex items-start gap-4">
                  <div className="bg-purple-100 p-3 rounded-xl">
                    <Star className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Mix Personalizado</h3>
                    <p className="text-gray-600 text-sm">
                      Algoritmo inteligente que cria sessões de estudo
                      personalizadas baseadas no seu desempenho.
                    </p>
                    <div className="flex items-center gap-2 mt-3">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-xs text-gray-500">IA integrada</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>

        {/* CTA Final - Mais compacto */}
        <div className="px-4 pb-16">
          <motion.div
            className="max-w-3xl mx-auto text-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.4 }}
          >
            <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-3xl p-1 shadow-2xl">
              <div className="bg-white/95 backdrop-blur-lg rounded-3xl p-8">
                <motion.div
                  className="inline-flex items-center gap-2 bg-gradient-to-r from-green-100 to-blue-100 px-4 py-2 rounded-full mb-4"
                  whileHover={{ scale: 1.05 }}
                >
                  <Sparkles className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-gray-700">100% Gratuito na versão Beta</span>
                </motion.div>

                <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-3">
                  Teste agora e depois explore o MedEvo completo
                </h3>

                <p className="text-gray-600 mb-6">
                  Comece testando pediatria no PedBook, depois acesse o MedEvo gratuitamente para todas as especialidades.
                </p>

                <div className="space-y-4">
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button
                      onClick={() => navigate('/estudos/filtros')}
                      size="lg"
                      className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg rounded-2xl shadow-xl"
                    >
                      <Play className="h-5 w-5 mr-3" />
                      Testar Pediatria no PedBook
                      <ArrowRight className="h-5 w-5 ml-3" />
                    </Button>
                  </motion.div>

                  <div className="flex items-center gap-3 text-sm text-gray-500">
                    <div className="flex-1 h-px bg-gray-200"></div>
                    <span>ou</span>
                    <div className="flex-1 h-px bg-gray-200"></div>
                  </div>

                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button
                      variant="outline"
                      onClick={() => window.open('https://medevo.com.br', '_blank')}
                      className="w-full sm:w-auto bg-white/80 border-2 border-gray-200 hover:border-purple-300 px-8 py-3 rounded-2xl"
                    >
                      <BookOpen className="h-5 w-5 mr-3" />
                      Acessar MedEvo Completo (Grátis)
                    </Button>
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </main>

      <Footer />

      {/* Tutorial Modal */}
      <PedBookTutorial
        isOpen={showTutorial}
        onClose={handleCloseTutorial}
        onStartStudy={handleStartStudy}
      />
    </div>
  );
};

export default PediatricStudyIntro;
