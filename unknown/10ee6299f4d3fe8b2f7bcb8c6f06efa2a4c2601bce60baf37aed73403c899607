
@media (max-width: 768px) {
  .dialog-content {
    padding: 0;
    border-radius: 15px;
    max-height: 85dvh !important;
    overflow-y: auto;
    width: 100% !important;
    max-width: calc(100vw - 2rem) !important;
  }

  .mobile-feedback-dialog {
    width: 100% !important;
    height: auto !important;
    overflow-y: auto !important;
  }

  input {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
}

/* Mobile feedback dialog specific adjustments */
.mobile-feedback-dialog .container {
  padding: 1rem !important;
  width: 100% !important;
  max-width: 100% !important;
}

.mobile-feedback-dialog form {
  padding: 1rem !important;
  width: 100% !important;
}

.mobile-feedback-dialog textarea {
  min-height: 100px !important;
}

/* Mobile feedback form - ensure all contents fit */
.mobile-feedback-form {
  width: 100% !important;
  box-sizing: border-box !important;
  overflow: visible !important;
}

.mobile-feedback-form .SelectTrigger {
  width: 100% !important;
  box-sizing: border-box !important;
}

.mobile-feedback-form input,
.mobile-feedback-form textarea,
.mobile-feedback-form button {
  width: 100% !important;
  box-sizing: border-box !important;
}

.mobile-tabs-list {
  flex-wrap: wrap !important;
  justify-content: center !important;
}

/* Add smooth transition for active state */
.active\:scale-95:active {
  transform: scale(0.95);
}

/* Add smooth transition for hover state */
.hover\:scale-105:hover {
  transform: scale(1.05);
}
.hover\:bg-gray-50:hover{
  background: none !important;
}

/* Mobile navigation container with very high z-index and shadow */
.mobile-nav-container {
  z-index: 100 !important;
  box-shadow: 0px -4px 20px rgba(0, 0, 0, 0.1);
  padding-bottom: env(safe-area-inset-bottom, 8px);
  padding-top: 4px; /* Adicionar padding superior para evitar corte */
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-top: none; /* Remover borda superior que pode estar cortando */
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  /* Impedir que o teclado virtual empurre a barra de navegação para cima */
  height: auto !important;
  /* Propriedade específica para iOS */
  transform: translateZ(0);
  /* Garantir que a barra de navegação fique acima do teclado virtual */
  will-change: transform;
}

/* Classe para esconder a barra de navegação ao rolar para baixo */
.mobile-nav-hidden {
  transform: translateY(100%);
  opacity: 0;
}

/* Indicador de página atual mais visível */
.mobile-nav-container button[aria-current="page"]::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 4px;
  height: 4px;
  background-color: rgba(var(--primary-rgb), 1);
  border-radius: 50%;
  transform: translateX(-50%);
}

/* Efeito de ripple para feedback visual ao tocar */
.mobile-nav-container button {
  position: relative;
  overflow: hidden;
  min-height: 44px;
}

/* Melhorar acessibilidade com indicador de foco */
.mobile-nav-container button:focus-visible {
  outline: 2px solid rgba(var(--primary-rgb), 0.7);
  outline-offset: 2px;
  border-radius: 8px;
}

.mobile-nav-container button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(var(--primary-rgb), 0.3);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.mobile-nav-container button:active::after {
  opacity: 1;
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 0.5;
  }
  100% {
    transform: scale(20);
    opacity: 0;
  }
}

@keyframes pulse-subtle {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

.animate-pulse-subtle {
  animation: pulse-subtle 3s ease-in-out infinite;
}



/* Fix for dropdown menu to appear above the navigation */
.DropdownMenuContent {
  z-index: 101 !important;
}

/* Add space for content to not be hidden behind the mobile navigation */
@media (max-width: 640px) {
  main {
    padding-bottom: var(--mobile-nav-height, 65px) !important;
    transition: padding-bottom 0.3s ease-in-out;
  }
}

/* Regras específicas para lidar com o teclado virtual */
@media (max-width: 640px) {
  /* Quando o teclado está aberto (detectado por altura da viewport) */
  @media (max-height: 500px) {
    /* Garantir que a barra de navegação permaneça visível */
    .mobile-nav-container {
      position: fixed !important;
      bottom: 0 !important;
      transform: none !important;
      opacity: 1 !important;
    }
  }
}

/* Regras específicas para iOS */
@supports (-webkit-touch-callout: none) {
  .mobile-nav-container {
    /* Usar posição absoluta em relação à viewport visual */
    position: fixed !important;
    bottom: 0 !important;
    /* Garantir que a barra de navegação fique acima do teclado */
    z-index: 9999 !important;
  }

  /* Impedir que o conteúdo seja empurrado para cima quando o teclado é aberto */
  body.keyboard-open {
    height: 100% !important;
    overflow: hidden !important;
  }
}

/* Estilos específicos para quando o teclado está visível */
.mobile-nav-container.keyboard-visible {
  /* Garantir que a barra de navegação fique acima do teclado */
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 9999 !important;
  /* Adicionar uma sombra mais forte para destacar a barra */
  box-shadow: 0px -4px 25px rgba(0, 0, 0, 0.15) !important;
  /* Aumentar a opacidade do fundo para melhor visibilidade */
  background-color: rgba(255, 255, 255, 0.98) !important;
  /* Garantir que a barra não seja escondida */
  transform: translateY(0) !important;
  opacity: 1 !important;
  /* Adicionar uma borda superior sutil */
  border-top: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* Versão para modo escuro */
.dark .mobile-nav-container.keyboard-visible {
  background-color: rgba(15, 23, 42, 0.98) !important;
  border-top: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* Estilos específicos para a página de interações medicamentosas */
.medication-pill {
  @apply px-3 py-1.5 rounded-full flex items-center gap-1.5 border;
  @apply transition-all duration-300;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.medication-pill-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
}

@media (max-width: 640px) {
  .medication-pill {
    flex: 1 1 calc(50% - 8px);
    justify-content: center;
    min-width: 120px;
  }

  .interaction-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .interaction-severity {
    width: 100%;
    text-align: center;
  }

  /* Melhorar o espaço para os cartões de interação em dispositivos móveis */
  .p-4.rounded-md.mb-4 {
    padding: 12px !important;
    margin-bottom: 12px !important;
  }

  /* Reduzir o padding interno dos blocos informativos */
  .bg-white\/80.dark\:bg-gray-800\/40.p-3.rounded-md,
  .bg-white.dark\:bg-gray-800\/50.p-3.rounded-md {
    padding: 10px !important;
  }

  /* Aumentar o espaço disponível para o texto */
  .font-semibold.text-gray-800.dark\:text-gray-200.break-words {
    font-size: 0.95rem !important;
    line-height: 1.4 !important;
  }
}

/* Melhorar a legibilidade em dispositivos móveis */
@media (max-width: 480px) {
  .interaction-content p,
  .interaction-content h5 {
    font-size: 0.95rem;
    line-height: 1.5;
  }

  /* Remover margens e paddings desnecessários para maximizar o espaço */
  .rounded-md.mb-3 {
    margin-bottom: 8px !important;
  }

  .mt-4 {
    margin-top: 10px !important;
  }

  /* Garantir que badges e outros elementos não ocupem espaço desnecessário */
  .text-xs.font-normal.rounded-full {
    white-space: nowrap;
  }
}
