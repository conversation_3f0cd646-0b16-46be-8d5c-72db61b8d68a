
import React, { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Link } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Card, CardContent } from "@/components/ui/card";
import {
  Baby, Search, Filter, ChevronDown, X, Folder,
  FolderOpen, ChevronRight, Info, ArrowUpDown, Plus, Minus,
  Home, ArrowLeft, Pill, AlertTriangle, CheckCircle, XCircle
} from "lucide-react";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { supabase } from "@/integrations/supabase/client";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";

// Definição dos tipos de dados
interface Medication {
  id: string;
  name: string;
  compatibility_level: string;
  usage_description: string;
  additional_info?: string;
  efeitos_no_lactente?: string;
  alternativas_seguras?: string[];
  orientacoes_uso?: string;
}

interface Subsection {
  id: string;
  name: string;
  description?: string;
  parent_subsection_id?: string;
  nesting_level: number;
  medications: Medication[];
  subsections?: Subsection[];
}

interface Section {
  id: string;
  name: string;
  description?: string;
  subsections?: Subsection[];
  medications?: Medication[];
}

interface BreastfeedingData {
  sections: Section[];
}

interface SearchResult {
  id: string;
  name: string;
  compatibility_level: string;
  usage_description: string;
  additional_info?: string;
  efeitos_no_lactente?: string;
  alternativas_seguras?: string[];
  orientacoes_uso?: string;
  section_name: string;
  subsection_name?: string;
}

// Tipo para controlar a navegação
type NavigationState = {
  currentLevel: 'sections' | 'subsections' | 'medications';
  currentSectionId: string | null;
  currentSubsectionId: string | null;
  breadcrumbs: Array<{id: string, name: string, type: 'section' | 'subsection'}>;
}

// Componente principal
const MedicationsBreastfeeding: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [activeFilter, setActiveFilter] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});
  const [expandedSubsections, setExpandedSubsections] = useState<Record<string, boolean>>({});
  const [navigation, setNavigation] = useState<NavigationState>({
    currentLevel: 'sections',
    currentSectionId: null,
    currentSubsectionId: null,
    breadcrumbs: []
  });
  const { toast } = useToast();

  console.log('🔄 [BreastfeedingMeds] Initiating component render');

  // Função para obter a cor com base no nível de compatibilidade
  const getCompatibilityColor = (level: string): string => {
    switch (level.toLowerCase()) {
      case 'verde':
        return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300';
      case 'amarelo':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'vermelho':
        return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  const getCompatibilityIcon = (level: string): JSX.Element => {
    switch (level.toLowerCase()) {
      case 'verde':
        return <CheckCircle className="h-3.5 w-3.5 text-green-500 mr-1.5" />;
      case 'amarelo':
        return <AlertTriangle className="h-3.5 w-3.5 text-yellow-500 mr-1.5" />;
      case 'vermelho':
        return <XCircle className="h-3.5 w-3.5 text-red-500 mr-1.5" />;
      default:
        return <span className="inline-block w-3 h-3 rounded-full bg-gray-500 mr-1.5"></span>;
    }
  };

  // Consulta para buscar estrutura completa
  const { data: breastfeedingData, isLoading, error, refetch } = useQuery({
    queryKey: ['breastfeeding-medications'],
    queryFn: async () => {
      console.log("🔍 [BreastfeedingMeds] Iniciando busca pela estrutura de amamentação");

      try {
        const { data, error } = await supabase.rpc('get_breastfeeding_structure');

        if (error) {
          console.error("❌ Erro ao buscar dados:", error);
          throw error;
        }

        console.log("✅ [BreastfeedingMeds] Dados recebidos com sucesso:",
                   data ? `${data.sections?.length || 0} seções encontradas` : "Sem dados");

        if (!data || !data.sections || !Array.isArray(data.sections)) {
          console.warn("⚠️ [BreastfeedingMeds] Estrutura de dados inválida:", data);
          throw new Error("Estrutura de dados inválida");
        }

        return data as BreastfeedingData;
      } catch (err: any) {
        console.error("❌ Erro ao buscar dados:", err);
        throw err;
      }
    }
  });

  // Consulta para buscar medicamentos por pesquisa
  const { data: searchResults, isLoading: isSearching } = useQuery({
    queryKey: ['search-breastfeeding-medications', searchQuery],
    queryFn: async () => {
      if (!searchQuery || searchQuery.length < 3) return null;

      console.log(`🔍 [BreastfeedingMeds] Pesquisando por: "${searchQuery}"`);

      try {
        const { data, error } = await supabase.rpc('search_breastfeeding_medications', {
          search_query: searchQuery,
          page_number: 1,
          items_per_page: 50
        });

        if (error) {
          console.error("❌ Erro ao pesquisar medicamentos:", error);
          return null;
        }

        console.log(`✅ [BreastfeedingMeds] Pesquisa concluída: ${data?.length || 0} resultados encontrados`);
        return data as SearchResult[];
      } catch (err: any) {
        console.error("❌ Erro na pesquisa de medicamentos:", err);
        return null;
      }
    },
    enabled: searchQuery.length >= 3
  });

  // Expandir seções e subseções específicas no carregamento inicial
  useEffect(() => {
    if (breastfeedingData?.sections) {
      const initialExpanded: Record<string, boolean> = {};
      breastfeedingData.sections.forEach(section => {
        // Inicializar apenas algumas seções como expandidas, não todas
        initialExpanded[section.id] = false;
      });
      setExpandedSections(initialExpanded);
      console.log(`📊 [BreastfeedingMeds] Inicializando seções: ${breastfeedingData.sections.length} seções`);
    }
  }, [breastfeedingData]);

  // Filtrar medicamentos por compatibilidade
  const toggleFilter = (level: string) => {
    console.log(`🔍 [BreastfeedingMeds] Alternando filtro: ${level}`);
    setActiveFilter(activeFilter === level ? null : level);
  };

  // Gerenciar exibição de seção
  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  // Gerenciar exibição de subseção
  const toggleSubsection = (subsectionId: string) => {
    setExpandedSubsections(prev => ({
      ...prev,
      [subsectionId]: !prev[subsectionId]
    }));
  };

  // Expandir todas as seções
  const expandAllSections = () => {
    if (!breastfeedingData?.sections) return;

    const allExpanded: Record<string, boolean> = {};
    breastfeedingData.sections.forEach(section => {
      allExpanded[section.id] = true;
    });
    setExpandedSections(allExpanded);
  };

  // Recolher todas as seções
  const collapseAllSections = () => {
    if (!breastfeedingData?.sections) return;

    const allCollapsed: Record<string, boolean> = {};
    breastfeedingData.sections.forEach(section => {
      allCollapsed[section.id] = false;
    });
    setExpandedSections(allCollapsed);
  };

  // Verificar recursivamente se um medicamento corresponde ao filtro
  const medicationMatchesFilter = (medication: Medication): boolean => {
    if (!activeFilter) return true;
    return medication.compatibility_level.toLowerCase() === activeFilter.toLowerCase();
  };

  // Verificar se uma seção tem medicamentos visíveis após filtragem
  const sectionHasVisibleMedications = (section: Section): boolean => {
    if (!activeFilter) return true;

    // Verificar medicamentos na própria seção
    if (section.medications?.some(med => medicationMatchesFilter(med))) {
      return true;
    }

    // Verificar medicamentos nas subseções (recursivamente)
    if (section.subsections?.some(subsection =>
      subsectionHasVisibleMedications(subsection)
    )) {
      return true;
    }

    return false;
  };

  // Verificar se uma subseção tem medicamentos visíveis após filtragem (recursivamente)
  const subsectionHasVisibleMedications = (subsection: Subsection): boolean => {
    if (!activeFilter) return true;

    // Verificar medicamentos na própria subseção
    if (subsection.medications && subsection.medications.some(med => medicationMatchesFilter(med))) {
      return true;
    }

    // Verificar medicamentos nas subseções aninhadas
    if (subsection.subsections?.some(subSubsection =>
      subsectionHasVisibleMedications(subSubsection)
    )) {
      return true;
    }

    return false;
  };

  // Componente para o Dialog de detalhes do medicamento
  const MedicationDetailsDialog = ({ medication }: { medication: Medication }) => {
    return (
      <DialogContent className="max-w-md rounded-xl max-h-[70dvh] overflow-hidden">
        <div className="flex items-center justify-between pb-2">
          <DialogTitle className="flex items-center gap-1.5 text-base">
            {getCompatibilityIcon(medication.compatibility_level)}
            {medication.name}
          </DialogTitle>
          <Badge className={`${getCompatibilityColor(medication.compatibility_level)} text-xs whitespace-nowrap flex-shrink-0 mr-6`}>
            {medication.compatibility_level}
          </Badge>
        </div>

        <div className="overflow-y-auto pr-1 max-h-[calc(70dvh-100px)] space-y-3">
          <div className="bg-yellow-50/50 dark:bg-yellow-900/10 p-2 rounded-lg border border-yellow-100 dark:border-yellow-900/20">
            <DialogDescription className="text-xs text-gray-700 dark:text-gray-300">
              {medication.usage_description}
            </DialogDescription>
          </div>

          {medication.additional_info && (
            <div className="text-xs text-gray-600 dark:text-gray-400 italic bg-gray-50 dark:bg-gray-800/50 p-2 rounded-lg">
              {medication.additional_info}
            </div>
          )}

          {medication.efeitos_no_lactente && (
            <div className="bg-pink-50/50 dark:bg-pink-900/10 p-2 rounded-lg border border-pink-100 dark:border-pink-900/20">
              <h5 className="font-medium mb-1 text-xs text-gray-900 dark:text-gray-100 flex items-center">
                <AlertTriangle className="h-3 w-3 text-pink-500 mr-1.5" />
                Efeitos no Lactente
              </h5>
              <div className="text-xs text-gray-700 dark:text-gray-300">{medication.efeitos_no_lactente}</div>
            </div>
          )}

          {medication.alternativas_seguras && medication.alternativas_seguras.length > 0 && (
            <div className="bg-green-50/50 dark:bg-green-900/10 p-2 rounded-lg border border-green-100 dark:border-green-900/20">
              <h5 className="font-medium mb-1 text-xs text-gray-900 dark:text-gray-100 flex items-center">
                <CheckCircle className="h-3 w-3 text-green-500 mr-1.5" />
                Alternativas Seguras
              </h5>
              <div className="text-xs text-gray-700 dark:text-gray-300 flex flex-wrap gap-1">
                {medication.alternativas_seguras.map((alt, index) => (
                  <span key={index} className="inline-block bg-green-100 dark:bg-green-900/30 px-1.5 py-0.5 rounded-full text-green-700 dark:text-green-300">
                    {alt}
                  </span>
                ))}
              </div>
            </div>
          )}

          {medication.orientacoes_uso && (
            <div className="bg-blue-50/50 dark:bg-blue-900/10 p-2 rounded-lg border border-blue-100 dark:border-blue-900/20">
              <h5 className="font-medium mb-1 text-xs text-gray-900 dark:text-gray-100 flex items-center">
                <Info className="h-3 w-3 text-blue-500 mr-1.5" />
                Orientações de Uso
              </h5>
              <div className="text-xs text-gray-700 dark:text-gray-300">{medication.orientacoes_uso}</div>
            </div>
          )}

          <div className="pt-2 flex justify-end">
            <DialogClose asChild>
              <Button variant="outline" className="rounded-full text-xs h-8 px-3">Fechar</Button>
            </DialogClose>
          </div>
        </div>
      </DialogContent>
    );
  };

  // Renderizar medicamento
  const renderMedication = (medication: Medication) => {
    if (!medicationMatchesFilter(medication)) {
      return null;
    }

    const borderColor = medication.compatibility_level.toLowerCase() === 'verde'
      ? 'border-l-green-400 dark:border-l-green-600'
      : medication.compatibility_level.toLowerCase() === 'amarelo'
        ? 'border-l-yellow-400 dark:border-l-yellow-600'
        : 'border-l-red-400 dark:border-l-red-600';

    return (
      <Dialog key={medication.id}>
        <DialogTrigger asChild>
          <Card
            className={`overflow-hidden border-l-4 ${borderColor} border-t border-r border-b border-gray-200 dark:border-gray-700 hover:shadow-md transition-all cursor-pointer p-2`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1.5">
                <div className="flex-shrink-0">
                  {getCompatibilityIcon(medication.compatibility_level)}
                </div>
                <span className="font-medium text-xs">{medication.name}</span>
              </div>
              <ChevronRight className="h-3.5 w-3.5 text-gray-400 flex-shrink-0" />
            </div>
          </Card>
        </DialogTrigger>
        <MedicationDetailsDialog medication={medication} />
      </Dialog>
    );
  };

  // Renderizar subseção (de forma recursiva)
  const renderSubsection = (subsection: Subsection, indentLevel: number = 1) => {
    if (!subsectionHasVisibleMedications(subsection)) return null;

    const isExpanded = expandedSubsections[subsection.id] || false;
    const hasNestedContent =
      (subsection.medications && subsection.medications.length > 0) ||
      (subsection.subsections && subsection.subsections.length > 0);

    return (
      <div key={subsection.id} className="mt-1.5" style={{ paddingLeft: `${indentLevel * 0.5}rem` }}>
        <Collapsible open={isExpanded} onOpenChange={open => toggleSubsection(subsection.id)}>
          <CollapsibleTrigger asChild>
            <button className={`w-full flex items-center p-1.5 rounded-md transition-all
              ${isExpanded ? 'bg-blue-50/80 dark:bg-blue-950/20' : 'hover:bg-blue-50/50 dark:hover:bg-blue-950/10'}
              gap-1.5 text-left`}
            >
              {hasNestedContent && (
                <ChevronRight className={`h-3.5 w-3.5 text-blue-600 dark:text-blue-400 transition-transform ${isExpanded ? 'rotate-90' : ''}`} />
              )}
              <span className="font-medium text-xs">{subsection.name}</span>
            </button>
          </CollapsibleTrigger>

          <CollapsibleContent>
            {subsection.description && (
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 mb-2 pl-2 border-l border-blue-200 dark:border-blue-900/50">{subsection.description}</p>
            )}

            {/* Render nested subsections if any */}
            {subsection.subsections && subsection.subsections.length > 0 && (
              subsection.subsections
                .filter(subSubsection => subsectionHasVisibleMedications(subSubsection))
                .map(subSubsection => renderSubsection(subSubsection, indentLevel + 1))
            )}

            {/* Render medications in this subsection */}
            {subsection.medications && subsection.medications.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                {subsection.medications
                  .filter(med => medicationMatchesFilter(med))
                  .map(medication => renderMedication(medication))}
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>
      </div>
    );
  };

  // Funções de navegação
  const navigateToSection = (section: Section) => {
    setNavigation({
      currentLevel: 'subsections',
      currentSectionId: section.id,
      currentSubsectionId: null,
      breadcrumbs: [{ id: section.id, name: section.name, type: 'section' }]
    });
  };

  const navigateToSubsection = (section: Section, subsection: Subsection) => {
    console.log('Navegando para subseção:', subsection.id, subsection.name);
    console.log('Subseção tem medicamentos:', subsection.medications?.length || 0);
    console.log('Subseção tem subseções:', subsection.subsections?.length || 0);

    // Verificar se a subseção tem subseções aninhadas visíveis
    const hasNestedSubsections = subsection.subsections &&
                                subsection.subsections.length > 0 &&
                                subsection.subsections.some(sub => subsectionHasVisibleMedications(sub));

    // Verificar se a subseção tem medicamentos
    const hasMedications = subsection.medications &&
                          subsection.medications.length > 0 &&
                          subsection.medications.some(med => medicationMatchesFilter(med));

    // Construir o caminho de breadcrumbs
    let breadcrumbs: Array<{id: string, name: string, type: 'section' | 'subsection'}> = [];

    // Se já estamos em uma subseção e navegando para outra subseção aninhada
    if (navigation.currentSubsectionId) {
      // Adicionar a nova subseção ao caminho atual
      breadcrumbs = [...navigation.breadcrumbs, {
        id: subsection.id,
        name: subsection.name,
        type: 'subsection'
      }];
    } else {
      // Caso contrário, construir o caminho do zero
      breadcrumbs = [
        { id: section.id, name: section.name, type: 'section' },
        { id: subsection.id, name: subsection.name, type: 'subsection' }
      ];
    }

    // Determinar o nível de navegação com base no conteúdo da subseção
    // Se tiver subseções aninhadas visíveis, mantemos no nível de subsections
    // Se tiver medicamentos, vamos para o nível de medications
    // Se não tiver nenhum dos dois, ainda assim vamos para medications para mostrar a mensagem de vazio
    const newLevel = hasNestedSubsections ? 'subsections' : 'medications';

    console.log('Definindo nível de navegação para:', newLevel);

    setNavigation({
      currentLevel: newLevel,
      currentSectionId: section.id,
      currentSubsectionId: subsection.id,
      breadcrumbs: breadcrumbs
    });
  };

  // Função para encontrar a subseção pai de uma subseção
  const findParentSubsection = (sectionId: string, subsectionId: string): Subsection | null => {
    const section = breastfeedingData?.sections.find(s => s.id === sectionId);
    if (!section || !section.subsections) return null;

    // Função recursiva para buscar em todas as subseções
    const findParent = (subsections: Subsection[], targetId: string): Subsection | null => {
      for (const sub of subsections) {
        if (sub.subsections) {
          // Verificar se alguma subseção direta é o pai
          const directChild = sub.subsections.find(s => s.id === targetId);
          if (directChild) return sub;

          // Buscar recursivamente
          const found = findParent(sub.subsections, targetId);
          if (found) return found;
        }
      }
      return null;
    };

    return findParent(section.subsections, subsectionId);
  };

  // Função recursiva para encontrar uma subseção por ID em qualquer nível de aninhamento
  const findSubsectionById = (subsections: Subsection[] | undefined, targetId: string): Subsection | undefined => {
    if (!subsections) return undefined;

    // Procurar diretamente nas subseções
    const directMatch = subsections.find(sub => sub.id === targetId);
    if (directMatch) return directMatch;

    // Procurar recursivamente nas subseções aninhadas
    for (const sub of subsections) {
      if (sub.subsections) {
        const nestedMatch = findSubsectionById(sub.subsections, targetId);
        if (nestedMatch) return nestedMatch;
      }
    }

    return undefined;
  };

  // Função para construir o caminho completo até uma subseção
  const buildBreadcrumbPath = (sectionId: string, subsectionId: string): Array<{id: string, name: string, type: 'section' | 'subsection'}> => {
    const section = breastfeedingData?.sections.find(s => s.id === sectionId);
    if (!section) return [];

    const path = [{ id: section.id, name: section.name, type: 'section' as const }];

    // Função recursiva para construir o caminho
    const buildPath = (subsections: Subsection[], targetId: string, currentPath: Array<{id: string, name: string, type: 'section' | 'subsection'}>): boolean => {
      for (const sub of subsections) {
        if (sub.id === targetId) {
          currentPath.push({ id: sub.id, name: sub.name, type: 'subsection' });
          return true;
        }

        if (sub.subsections) {
          currentPath.push({ id: sub.id, name: sub.name, type: 'subsection' });
          const found = buildPath(sub.subsections, targetId, currentPath);
          if (found) return true;
          currentPath.pop(); // Remover se não for o caminho correto
        }
      }
      return false;
    };

    if (section.subsections) {
      buildPath(section.subsections, subsectionId, path);
    }

    return path;
  };

  const navigateBack = () => {
    if (navigation.breadcrumbs.length <= 1) {
      // Voltar para o nível de seções
      setNavigation({
        currentLevel: 'sections',
        currentSectionId: null,
        currentSubsectionId: null,
        breadcrumbs: []
      });
      return;
    }

    // Voltar um nível na navegação
    const newBreadcrumbs = navigation.breadcrumbs.slice(0, -1);
    const lastItem = newBreadcrumbs[newBreadcrumbs.length - 1];

    if (lastItem.type === 'section') {
      // Voltar para o nível da seção
      setNavigation({
        currentLevel: 'subsections',
        currentSectionId: lastItem.id,
        currentSubsectionId: null,
        breadcrumbs: newBreadcrumbs
      });
    } else {
      // Voltar para uma subseção
      const parentSection = breastfeedingData?.sections.find(s => s.id === navigation.currentSectionId);

      setNavigation({
        currentLevel: 'subsections',
        currentSectionId: navigation.currentSectionId,
        currentSubsectionId: lastItem.id,
        breadcrumbs: newBreadcrumbs
      });
    }
  };

  // Renderização de seções em formato de cards
  const renderSectionCard = (section: Section) => {
    if (!sectionHasVisibleMedications(section)) return null;

    const totalMedications = countVisibleMedicationsInSection(section);

    return (
      <Card
        key={section.id}
        className="overflow-hidden border-l-4 border-l-pink-400 dark:border-l-pink-600 border-t border-r border-b border-gray-200 dark:border-gray-700 hover:shadow-md transition-all cursor-pointer"
        onClick={() => navigateToSection(section)}
      >
        <div className="p-3 flex flex-col h-full">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Folder className="h-4 w-4 text-pink-500 dark:text-pink-400 flex-shrink-0" />
              <h3 className="font-medium text-sm">{section.name}</h3>
            </div>
            <ChevronRight className="h-4 w-4 text-gray-400 flex-shrink-0" />
          </div>

          <div className="mt-2 pt-2 border-t border-gray-100 dark:border-gray-800 flex justify-between items-center">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {totalMedications} medicamento{totalMedications !== 1 ? 's' : ''}
            </span>
            {section.subsections && section.subsections.length > 0 && (
              <Badge variant="outline" className="text-xs">
                {section.subsections.length} subseção{section.subsections.length !== 1 ? 'ões' : ''}
              </Badge>
            )}
          </div>
        </div>
      </Card>
    );
  };

  // Função para contar medicamentos em uma subseção e suas subseções aninhadas
  const countAllMedicationsInSubsection = (subsection: Subsection): number => {
    let count = 0;

    // Contar medicamentos diretos
    if (subsection.medications) {
      count += subsection.medications.filter(med => medicationMatchesFilter(med)).length;
    }

    // Contar medicamentos em subseções aninhadas
    if (subsection.subsections) {
      subsection.subsections.forEach(nestedSub => {
        count += countAllMedicationsInSubsection(nestedSub);
      });
    }

    return count;
  };

  // Renderização de subseções em formato de cards
  const renderSubsectionCard = (section: Section, subsection: Subsection) => {
    if (!subsectionHasVisibleMedications(subsection)) return null;

    // Contar todos os medicamentos, incluindo os de subseções aninhadas
    const totalMedications = countAllMedicationsInSubsection(subsection);

    // Verificar se tem subseções aninhadas
    const hasNestedSubsections = subsection.subsections && subsection.subsections.length > 0 &&
      subsection.subsections.some(sub => subsectionHasVisibleMedications(sub));

    return (
      <Card
        key={subsection.id}
        className="overflow-hidden border-l-4 border-l-blue-400 dark:border-l-blue-600 border-t border-r border-b border-gray-200 dark:border-gray-700 hover:shadow-md transition-all cursor-pointer"
        onClick={() => navigateToSubsection(section, subsection)}
      >
        <div className="p-3 flex flex-col h-full">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <FolderOpen className="h-4 w-4 text-blue-500 dark:text-blue-400 flex-shrink-0" />
              <h3 className="font-medium text-sm">{subsection.name}</h3>
            </div>
            <ChevronRight className="h-4 w-4 text-gray-400 flex-shrink-0" />
          </div>

          <div className="mt-2 pt-2 border-t border-gray-100 dark:border-gray-800 flex justify-between items-center">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {totalMedications} medicamento{totalMedications !== 1 ? 's' : ''}
            </span>
            {hasNestedSubsections && (
              <Badge variant="outline" className="text-xs">
                {subsection.subsections?.filter(sub => subsectionHasVisibleMedications(sub)).length} subseção{subsection.subsections?.filter(sub => subsectionHasVisibleMedications(sub)).length !== 1 ? 'ões' : ''}
              </Badge>
            )}
          </div>
        </div>
      </Card>
    );
  };

  // Função auxiliar para contar medicamentos visíveis em uma seção
  const countVisibleMedicationsInSection = (section: Section): number => {
    let count = 0;

    // Contar medicamentos diretos da seção
    if (section.medications) {
      count += section.medications.filter(med => medicationMatchesFilter(med)).length;
    }

    // Contar medicamentos das subseções (incluindo aninhadas)
    if (section.subsections) {
      section.subsections.forEach(subsection => {
        count += countAllMedicationsInSubsection(subsection);
      });
    }

    return count;
  };

  // Renderização original de seção (para compatibilidade)
  const renderSection = (section: Section) => {
    if (!sectionHasVisibleMedications(section)) return null;

    const isExpanded = expandedSections[section.id] || false;
    const hasMedications = section.medications && section.medications.length > 0;
    const hasSubsections = section.subsections && section.subsections.length > 0;

    return (
      <Card key={section.id} className="mb-3 overflow-hidden border-b shadow-sm hover:shadow-md transition-all">
        <Collapsible open={isExpanded} onOpenChange={() => toggleSection(section.id)}>
          <CollapsibleTrigger asChild>
            <div className="p-3 cursor-pointer bg-gradient-to-r from-pink-50 to-white dark:from-slate-800/90 dark:to-slate-800/60 hover:bg-pink-50/80 dark:hover:bg-pink-900/20 transition-all">
              <div className="flex items-center gap-2">
                <div className={`p-1.5 rounded-full ${isExpanded ? 'bg-pink-100 dark:bg-pink-900/30' : 'bg-gray-100 dark:bg-gray-700/30'}`}>
                  {isExpanded ? (
                    <FolderOpen className="h-4 w-4 text-pink-600 dark:text-pink-400" />
                  ) : (
                    <Folder className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-base">{section.name}</h3>
                </div>
                <ChevronDown className={`h-4 w-4 transition-transform ${isExpanded ? 'transform rotate-180' : ''}`} />
              </div>
            </div>
          </CollapsibleTrigger>

          <CollapsibleContent>
            <div className="p-3 bg-white dark:bg-slate-800/90">
              {section.description && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{section.description}</p>
              )}

              {/* Medicamentos da seção */}
              {hasMedications && (
                <div className="mb-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                    {section.medications
                      .filter(med => medicationMatchesFilter(med))
                      .map(medication => renderMedication(medication))}
                  </div>
                </div>
              )}

              {/* Subseções */}
              {hasSubsections && (
                <div className="space-y-1">
                  {section.subsections
                    .filter(subsection => subsectionHasVisibleMedications(subsection))
                    .map(subsection => renderSubsection(subsection))}
                </div>
              )}

              {/* Exibir mensagem caso não tenha conteúdo visível */}
              {((!hasMedications || section.medications.every(med => !medicationMatchesFilter(med))) &&
               (!hasSubsections || section.subsections.every(sub => !subsectionHasVisibleMedications(sub)))) && (
                <p className="text-gray-500 italic text-center py-4">
                  Nenhum item encontrado nesta seção com os filtros atuais.
                </p>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    );
  };

  // Renderizar resultados da pesquisa
  const renderSearchResults = () => {
    if (!searchResults || searchResults.length === 0) {
      return (
        <div className="text-center p-6 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
          <p>Nenhum medicamento encontrado com este termo.</p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <p className="text-gray-600 dark:text-gray-400 text-sm">{searchResults.length} medicamento(s) encontrado(s):</p>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
          {searchResults.map((result) => (
            <Card
              key={result.id}
              className="overflow-hidden transition-all hover:shadow-md border-l-4 flex flex-col h-full"
              style={{
                borderLeftColor: result.compatibility_level.toLowerCase() === 'verde' ? 'rgb(34 197 94)' :
                                result.compatibility_level.toLowerCase() === 'amarelo' ? 'rgb(234 179 8)' :
                                'rgb(239 68 68)'
              }}
            >
              <div className="p-3 flex flex-col h-full">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 flex items-center text-sm">
                    {getCompatibilityIcon(result.compatibility_level)}
                    {result.name}
                  </h4>
                  <Badge className={`${getCompatibilityColor(result.compatibility_level)} text-xs`}>
                    {result.compatibility_level}
                  </Badge>
                </div>

                <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1 mb-2">
                  <Folder className="h-3.5 w-3.5" />
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="truncate max-w-[200px] inline-block">{result.section_name}</span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{result.section_name}</p>
                        {result.subsection_name && <p className="text-primary">{result.subsection_name}</p>}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  {result.subsection_name && (
                    <>
                      <ChevronRight className="h-3 w-3 mx-1" />
                      <span className="truncate max-w-[150px]">{result.subsection_name}</span>
                    </>
                  )}
                </div>

                <div className="flex-1">
                  <p className="text-xs text-gray-700 dark:text-gray-300">{result.usage_description}</p>
                  {result.additional_info && (
                    <p className="text-xs italic mt-2 pt-2 border-t border-gray-100 dark:border-gray-700 text-gray-600 dark:text-gray-400">
                      {result.additional_info}
                    </p>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  console.log(`🔄 [BreastfeedingMeds] Renderizando componente - isLoading: ${isLoading}, hasError: ${!!error}, seções disponíveis: ${breastfeedingData?.sections?.length || 0}`);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-pink-50 via-white to-pink-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800">
      <HelmetWrapper>
        <title>Medicamentos e Amamentação | PedBook</title>
        <meta
          name="description"
          content="Informações sobre segurança de medicamentos durante a amamentação para profissionais de saúde."
        />
      </HelmetWrapper>

      <Header />

      <main className="flex-1 container mx-auto px-4 py-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center gap-4 mb-8">
            <Link to="/">
              <Button
                variant="ghost"
                size="icon"
                className="hover:bg-primary/10 dark:hover:bg-primary/20"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>

            <div className="text-center flex-1 space-y-3">
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100">
                Medicamentos e Amamentação
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                Segurança de medicamentos durante o período de amamentação
              </p>
            </div>
          </div>

          <Card className="p-4 mb-4 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border-pink-100 dark:border-slate-700 shadow-lg rounded-xl">
            <CardContent className="p-0">
              <div className="prose max-w-none dark:prose-invert text-sm">
                {/* Texto descritivo removido conforme solicitado */}

                <div className="flex flex-col items-center justify-center py-4 max-w-2xl mx-auto">
                  {/* Barra de pesquisa centralizada */}
                  <div className="relative w-full mb-4">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Pesquisar medicamento ou categoria..."
                      className="pl-10 w-full bg-white dark:bg-slate-900"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    {searchQuery && (
                      <button
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                        onClick={() => setSearchQuery("")}
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>

                  {/* Filtros compactos */}
                  <div className="flex justify-center w-full mb-2">
                    <div className="inline-flex items-center rounded-full border border-gray-200 dark:border-gray-700 p-0.5 bg-gray-50 dark:bg-gray-800/50 shadow-sm">
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`h-7 px-2 rounded-full flex items-center gap-1 ${
                          activeFilter === 'verde'
                            ? 'bg-green-100 text-green-700 dark:bg-green-900/40 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/60'
                            : 'hover:bg-gray-100 dark:hover:bg-gray-700/50'
                        }`}
                        onClick={() => toggleFilter('verde')}
                      >
                        <CheckCircle className="h-3 w-3 text-green-500" />
                        <span className="text-xs">Seguros</span>
                      </Button>

                      <Button
                        variant="ghost"
                        size="sm"
                        className={`h-7 px-2 rounded-full flex items-center gap-1 ${
                          activeFilter === 'amarelo'
                            ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/40 dark:text-yellow-400 hover:bg-yellow-200 dark:hover:bg-yellow-900/60'
                            : 'hover:bg-gray-100 dark:hover:bg-gray-700/50'
                        }`}
                        onClick={() => toggleFilter('amarelo')}
                      >
                        <AlertTriangle className="h-3 w-3 text-yellow-500" />
                        <span className="text-xs">Uso Criterioso</span>
                      </Button>

                      <Button
                        variant="ghost"
                        size="sm"
                        className={`h-7 px-2 rounded-full flex items-center gap-1 ${
                          activeFilter === 'vermelho'
                            ? 'bg-red-100 text-red-700 dark:bg-red-900/40 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-900/60'
                            : 'hover:bg-gray-100 dark:hover:bg-gray-700/50'
                        }`}
                        onClick={() => toggleFilter('vermelho')}
                      >
                        <XCircle className="h-3 w-3 text-red-500" />
                        <span className="text-xs">Contraindicados</span>
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Conteúdo principal - Combinando categorias e resultados de pesquisa */}
                {isLoading ? (
                  <div className="space-y-3">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <Skeleton key={i} className="h-14 w-full rounded-lg" />
                    ))}
                  </div>
                ) : error ? (
                  <div className="p-6 text-center text-red-500 bg-red-50 dark:bg-red-900/20 dark:text-red-300 rounded-lg border border-red-200 dark:border-red-800">
                    <p className="font-medium">Erro ao carregar dados. Por favor, tente novamente mais tarde.</p>
                    <p className="text-sm mt-2">Detalhes técnicos: {(error as any)?.message || 'Erro desconhecido'}</p>
                    <Button
                      variant="outline"
                      className="mt-4 border-red-200 text-red-600 dark:border-red-800 dark:text-red-400"
                      onClick={() => {
                        toast({
                          title: "Recarregando dados...",
                          duration: 2000,
                        });
                        refetch();
                      }}
                    >
                      Tentar novamente
                    </Button>
                  </div>
                ) : !breastfeedingData?.sections || breastfeedingData.sections.length === 0 ? (
                  <div className="p-6 text-center text-amber-500 bg-amber-50 dark:bg-amber-900/20 dark:text-amber-300 rounded-lg">
                    <p>Nenhum dado disponível. Os registros ainda não foram importados.</p>
                  </div>
                ) : (
                  <>
                    {/* Resultados da pesquisa (mostrados quando há uma pesquisa ativa) */}
                    {searchQuery.length >= 3 && (
                      <div className="mb-6 border-b pb-6 border-gray-200 dark:border-gray-700">
                        <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                          <Search className="h-4 w-4 text-pink-500" />
                          Resultados da pesquisa
                        </h3>

                        {isSearching ? (
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                            {Array.from({ length: 3 }).map((_, i) => (
                              <Skeleton key={i} className="h-32 w-full rounded-lg" />
                            ))}
                          </div>
                        ) : !searchResults || searchResults.length === 0 ? (
                          <div className="text-center p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                            <p className="text-gray-600 dark:text-gray-400">Nenhum medicamento encontrado com este termo.</p>
                          </div>
                        ) : (
                          <div className="space-y-4">
                            <p className="text-gray-600 dark:text-gray-400 text-sm">{searchResults.length} medicamento(s) encontrado(s):</p>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                              {searchResults.map((result) => (
                                <Dialog key={result.id}>
                                  <DialogTrigger asChild>
                                    <Card
                                      className={`overflow-hidden border-l-4 ${
                                        result.compatibility_level.toLowerCase() === 'verde'
                                          ? 'border-l-green-400 dark:border-l-green-600'
                                          : result.compatibility_level.toLowerCase() === 'amarelo'
                                            ? 'border-l-yellow-400 dark:border-l-yellow-600'
                                            : 'border-l-red-400 dark:border-l-red-600'
                                      } border-t border-r border-b border-gray-200 dark:border-gray-700 hover:shadow-md transition-all cursor-pointer p-2`}
                                    >
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-1.5">
                                          <div className="flex-shrink-0">
                                            {getCompatibilityIcon(result.compatibility_level)}
                                          </div>
                                          <span className="font-medium text-xs">{result.name}</span>
                                        </div>
                                        <ChevronRight className="h-3.5 w-3.5 text-gray-400 flex-shrink-0" />
                                      </div>
                                    </Card>
                                  </DialogTrigger>
                                  <DialogContent className="max-w-md rounded-xl max-h-[70dvh] overflow-hidden">
                                    <div className="flex items-center justify-between pb-2">
                                      <DialogTitle className="flex items-center gap-1.5 text-base">
                                        {getCompatibilityIcon(result.compatibility_level)}
                                        {result.name}
                                      </DialogTitle>
                                      <Badge className={`${getCompatibilityColor(result.compatibility_level)} text-xs whitespace-nowrap flex-shrink-0 mr-6`}>
                                        {result.compatibility_level}
                                      </Badge>
                                    </div>

                                    <div className="overflow-y-auto pr-1 max-h-[calc(70dvh-100px)] space-y-3">
                                      <div className="bg-yellow-50/50 dark:bg-yellow-900/10 p-2 rounded-lg border border-yellow-100 dark:border-yellow-900/20">
                                        <DialogDescription className="text-xs text-gray-700 dark:text-gray-300">
                                          {result.usage_description}
                                        </DialogDescription>
                                      </div>

                                      {result.additional_info && (
                                        <div className="text-xs text-gray-600 dark:text-gray-400 italic bg-gray-50 dark:bg-gray-800/50 p-2 rounded-lg">
                                          {result.additional_info}
                                        </div>
                                      )}

                                      {result.efeitos_no_lactente && (
                                        <div className="bg-pink-50/50 dark:bg-pink-900/10 p-2 rounded-lg border border-pink-100 dark:border-pink-900/20">
                                          <h5 className="font-medium mb-1 text-xs text-gray-900 dark:text-gray-100 flex items-center">
                                            <AlertTriangle className="h-3 w-3 text-pink-500 mr-1.5" />
                                            Efeitos no Lactente
                                          </h5>
                                          <div className="text-xs text-gray-700 dark:text-gray-300">{result.efeitos_no_lactente}</div>
                                        </div>
                                      )}

                                      {result.alternativas_seguras && result.alternativas_seguras.length > 0 && (
                                        <div className="bg-green-50/50 dark:bg-green-900/10 p-2 rounded-lg border border-green-100 dark:border-green-900/20">
                                          <h5 className="font-medium mb-1 text-xs text-gray-900 dark:text-gray-100 flex items-center">
                                            <CheckCircle className="h-3 w-3 text-green-500 mr-1.5" />
                                            Alternativas Seguras
                                          </h5>
                                          <div className="text-xs text-gray-700 dark:text-gray-300 flex flex-wrap gap-1">
                                            {result.alternativas_seguras.map((alt, index) => (
                                              <span key={index} className="inline-block bg-green-100 dark:bg-green-900/30 px-1.5 py-0.5 rounded-full text-green-700 dark:text-green-300">
                                                {alt}
                                              </span>
                                            ))}
                                          </div>
                                        </div>
                                      )}

                                      {result.orientacoes_uso && (
                                        <div className="bg-blue-50/50 dark:bg-blue-900/10 p-2 rounded-lg border border-blue-100 dark:border-blue-900/20">
                                          <h5 className="font-medium mb-1 text-xs text-gray-900 dark:text-gray-100 flex items-center">
                                            <Info className="h-3 w-3 text-blue-500 mr-1.5" />
                                            Orientações de Uso
                                          </h5>
                                          <div className="text-xs text-gray-700 dark:text-gray-300">{result.orientacoes_uso}</div>
                                        </div>
                                      )}

                                      <div className="pt-2 flex justify-end">
                                        <DialogClose asChild>
                                          <Button variant="outline" className="rounded-full text-xs h-8 px-3">Fechar</Button>
                                        </DialogClose>
                                      </div>
                                    </div>
                                  </DialogContent>
                                </Dialog>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Navegação hierárquica com cards */}
                    <div className="space-y-4">
                      {/* Botão simplificado para voltar ao nível anterior */}
                      {/* Botão de voltar */}
                      {navigation.currentLevel !== 'sections' && (
                        <div className="mb-3">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition-colors border border-gray-200 dark:border-gray-700"
                            onClick={navigateBack}
                          >
                            <ArrowLeft className="h-3.5 w-3.5" />
                            <span>Voltar</span>
                          </Button>
                        </div>
                      )}

                      {/* Título da seção atual */}
                      <div className="mb-4">
                        {navigation.currentLevel === 'sections' ? (
                          <h3 className="text-lg font-medium flex items-center gap-2">
                            <Folder className="h-4 w-4 text-pink-500 dark:text-pink-400" />
                            Navegue pelas Categorias
                          </h3>
                        ) : navigation.currentLevel === 'subsections' ? (
                          <h3 className="text-lg font-medium flex items-center gap-2">
                            <FolderOpen className="h-4 w-4 text-blue-500 dark:text-blue-400" />
                            {breastfeedingData.sections.find(s => s.id === navigation.currentSectionId)?.name || 'Subseções'}
                            {breastfeedingData.sections.find(s => s.id === navigation.currentSectionId)?.description && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="rounded-full h-6 w-6 p-0 flex items-center justify-center ml-1"
                                onClick={() => {
                                  // Aqui você pode implementar a lógica para mostrar a descrição
                                  // Por exemplo, usando um estado local para controlar a visibilidade de um modal
                                  toast({
                                    title: "Informações da Categoria",
                                    description: breastfeedingData.sections.find(s => s.id === navigation.currentSectionId)?.description,
                                    duration: 5000,
                                  });
                                }}
                              >
                                <Info className="h-3 w-3 text-blue-500" />
                              </Button>
                            )}
                          </h3>
                        ) : (
                          <h3 className="text-lg font-medium flex items-center gap-2">
                            <Pill className="h-4 w-4 text-green-500 dark:text-green-400" />
                            {breastfeedingData.sections
                              .find(s => s.id === navigation.currentSectionId)
                              ?.subsections?.find(sub => sub.id === navigation.currentSubsectionId)?.name || 'Medicamentos'}
                            {breastfeedingData.sections
                              .find(s => s.id === navigation.currentSectionId)
                              ?.subsections?.find(sub => sub.id === navigation.currentSubsectionId)?.description && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="rounded-full h-6 w-6 p-0 flex items-center justify-center ml-1"
                                onClick={() => {
                                  // Aqui você pode implementar a lógica para mostrar a descrição
                                  toast({
                                    title: "Informações da Subcategoria",
                                    description: breastfeedingData.sections
                                      .find(s => s.id === navigation.currentSectionId)
                                      ?.subsections?.find(sub => sub.id === navigation.currentSubsectionId)?.description,
                                    duration: 5000,
                                  });
                                }}
                              >
                                <Info className="h-3 w-3 text-blue-500" />
                              </Button>
                            )}
                          </h3>
                        )}
                      </div>

                      {/* Botão de voltar removido daqui e movido para cima */}

                      {/* Conteúdo baseado no nível de navegação */}
                      {navigation.currentLevel === 'sections' ? (
                        // Nível 1: Mostrar todas as seções como cards
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                          {breastfeedingData.sections
                            .filter(section => sectionHasVisibleMedications(section))
                            .map(section => renderSectionCard(section))}

                          {/* Mostrar mensagem apenas se houver um filtro ativo */}
                          {activeFilter && breastfeedingData.sections.filter(section => sectionHasVisibleMedications(section)).length === 0 && (
                            <div className="col-span-full p-6 text-center bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                              <p className="text-gray-500 dark:text-gray-400">
                                Nenhuma medicação encontrada com o filtro selecionado.
                              </p>
                              <Button
                                variant="outline"
                                size="sm"
                                className="mt-2"
                                onClick={() => setActiveFilter(null)}
                              >
                                Limpar filtro
                              </Button>
                            </div>
                          )}
                        </div>
                      ) : navigation.currentLevel === 'subsections' ? (
                        // Nível 2: Mostrar subseções da seção selecionada
                        <>
                          {/* Medicamentos diretos da seção */}
                          {breastfeedingData.sections.find(s => s.id === navigation.currentSectionId)?.medications?.filter(med => medicationMatchesFilter(med)).length > 0 && (
                            <div className="mb-6">
                              <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
                                <Pill className="h-4 w-4 text-green-500" />
                                Medicamentos nesta categoria
                              </h4>
                              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                                {breastfeedingData.sections
                                  .find(s => s.id === navigation.currentSectionId)
                                  ?.medications
                                  ?.filter(med => medicationMatchesFilter(med))
                                  .map(medication => renderMedication(medication))}
                              </div>
                            </div>
                          )}

                          {/* Subseções como cards */}
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            {/* Se estamos no nível de seção principal, mostrar suas subseções diretas */}
                            {!navigation.currentSubsectionId && breastfeedingData.sections
                              .find(s => s.id === navigation.currentSectionId)
                              ?.subsections
                              ?.filter(subsection => subsectionHasVisibleMedications(subsection))
                              .map(subsection => renderSubsectionCard(
                                breastfeedingData.sections.find(s => s.id === navigation.currentSectionId)!,
                                subsection
                              ))}

                            {/* Se estamos no nível de subseção, mostrar suas subseções aninhadas */}
                            {navigation.currentSubsectionId &&
                              breastfeedingData.sections
                                .find(s => s.id === navigation.currentSectionId)
                                ?.subsections
                                ?.find(sub => sub.id === navigation.currentSubsectionId)
                                ?.subsections
                                ?.filter(nestedSub => subsectionHasVisibleMedications(nestedSub))
                                .map(nestedSubsection => renderSubsectionCard(
                                  breastfeedingData.sections.find(s => s.id === navigation.currentSectionId)!,
                                  nestedSubsection
                                ))
                            }

                            {/* Mensagem quando não há subseções, mas apenas se houver um filtro ativo */}
                            {activeFilter && (
                              ((!navigation.currentSubsectionId &&
                                breastfeedingData.sections.find(s => s.id === navigation.currentSectionId)
                                  ?.subsections?.filter(sub => subsectionHasVisibleMedications(sub)).length === 0) ||
                               (navigation.currentSubsectionId &&
                                (!breastfeedingData.sections.find(s => s.id === navigation.currentSectionId)
                                  ?.subsections?.find(sub => sub.id === navigation.currentSubsectionId)?.subsections ||
                                 breastfeedingData.sections.find(s => s.id === navigation.currentSectionId)
                                  ?.subsections?.find(sub => sub.id === navigation.currentSubsectionId)
                                  ?.subsections?.filter(nestedSub => subsectionHasVisibleMedications(nestedSub)).length === 0))) && (
                               <div className="col-span-full p-6 text-center bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                                 <p className="text-gray-500 dark:text-gray-400">
                                   Nenhuma subseção encontrada com o filtro selecionado.
                                 </p>
                               </div>
                             )
                            )}
                          </div>
                        </>
                      ) : (
                        // Nível 3: Mostrar medicamentos da subseção selecionada
                        <>
                          {/* Log para debug */}
                          {console.log('Renderizando medicamentos para subseção:',
                            navigation.currentSubsectionId,
                            'em seção:',
                            navigation.currentSectionId,
                            'medicamentos:',
                            breastfeedingData.sections
                              .find(s => s.id === navigation.currentSectionId)
                              ?.subsections?.find(sub => sub.id === navigation.currentSubsectionId)
                              ?.medications?.length || 0
                          )}

                          {/* Medicamentos da subseção */}
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                            {/* Medicamentos diretos da subseção */}
                            {/* Usar a função global para encontrar uma subseção em qualquer nível de aninhamento */}
                            {(() => {

                              // Encontrar a seção
                              const section = breastfeedingData.sections.find(s => s.id === navigation.currentSectionId);
                              if (!section) return null;

                              // Encontrar a subseção em qualquer nível
                              const subsection = findSubsectionById(section.subsections, navigation.currentSubsectionId!);

                              console.log('Subseção encontrada:', subsection?.id, subsection?.name);
                              console.log('Medicamentos na subseção:', subsection?.medications?.length || 0);

                              // Renderizar os medicamentos se a subseção for encontrada
                              if (subsection?.medications) {
                                return subsection.medications
                                  .filter(med => medicationMatchesFilter(med))
                                  .map(medication => renderMedication(medication));
                              }

                              return null;
                            })()}

                            {/* Verificar se há medicamentos na subseção atual */}
                            {(() => {

                              // Encontrar a seção
                              const section = breastfeedingData.sections.find(s => s.id === navigation.currentSectionId);
                              if (!section) return null;

                              // Encontrar a subseção em qualquer nível
                              const subsection = findSubsectionById(section.subsections, navigation.currentSubsectionId!);

                              // Verificar se não há medicamentos ou se todos estão filtrados
                              const hasMedications = subsection?.medications &&
                                                    subsection.medications.length > 0 &&
                                                    subsection.medications.some(med => medicationMatchesFilter(med));

                              // Mostrar mensagem apenas se houver um filtro ativo
                              if (!hasMedications && activeFilter) {
                                return (
                                  <div className="col-span-full p-6 text-center bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                                    <p className="text-gray-500 dark:text-gray-400">
                                      Nenhum medicamento encontrado com o filtro selecionado.
                                    </p>
                                  </div>
                                );
                              }

                              return null;
                            })()}
                          </div>

                          {/* Verificar se há subseções aninhadas e exibi-las como cards */}
                          {(() => {

                            // Encontrar a seção
                            const section = breastfeedingData.sections.find(s => s.id === navigation.currentSectionId);
                            if (!section) return null;

                            // Encontrar a subseção em qualquer nível
                            const subsection = findSubsectionById(section.subsections, navigation.currentSubsectionId!);

                            // Verificar se tem subseções aninhadas visíveis
                            const hasNestedSubsections = subsection?.subsections &&
                                                        subsection.subsections.length > 0 &&
                                                        subsection.subsections.some(sub => subsectionHasVisibleMedications(sub));

                            console.log('Subseção tem subseções aninhadas:', hasNestedSubsections);

                            if (hasNestedSubsections && subsection) {
                              return (
                                <div className="mt-6">
                                  <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
                                    <FolderOpen className="h-4 w-4 text-blue-500" />
                                    Subseções
                                  </h4>
                                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                    {subsection.subsections
                                      ?.filter(nestedSub => subsectionHasVisibleMedications(nestedSub))
                                      .map(nestedSubsection => renderSubsectionCard(
                                        section,
                                        nestedSubsection
                                      ))}
                                  </div>
                                </div>
                              );
                            }

                            return null;
                          })()}
                        </>
                      )}
                    </div>
                  </>
                )}

                <div className="bg-gradient-to-r from-blue-50 via-blue-50/80 to-blue-50 dark:from-blue-900/20 dark:via-blue-900/10 dark:to-blue-900/20 p-4 rounded-lg mt-5 border border-blue-100 dark:border-blue-800/30 text-center shadow-sm">
                  <div className="flex flex-col items-center gap-2">
                    <div className="bg-white dark:bg-gray-800 p-1.5 rounded-full shadow-sm">
                      <Info className="h-4 w-4 text-blue-500" />
                    </div>
                    <div className="space-y-1.5">
                      <p className="text-sm font-medium text-gray-700 dark:text-gray-200">
                        Esta ferramenta é apenas um guia e não substitui a avaliação clínica individual.
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Fonte: Dados adaptados do Ministério da Saúde, complementados com informações relevantes para o nosso sistema.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Botão fixo removido conforme solicitado */}

      <Footer />
    </div>
  );
};

export default MedicationsBreastfeeding;
