
import { useState, useEffect } from "react";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { useSession } from "@supabase/auth-helpers-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { ChevronLeft } from "lucide-react";
import { Link } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import ProfileForm from "@/components/settings/ProfileForm";
import AvatarUpload from "@/components/settings/AvatarUpload";
import DeleteAccountForm from "@/components/settings/DeleteAccountForm";
import { getThemeClasses } from "@/components/ui/theme-utils";
import { SiteFeedbackDialog } from "@/components/feedback/SiteFeedbackDialog";
import { FeedbackPage } from "@/pages/feedback/FeedbackPage";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

const Settings = () => {
  const session = useSession();
  const { toast } = useToast();
  const [profile, setProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [showFeedbackDialog, setShowFeedbackDialog] = useState(false);

  useEffect(() => {
    if (session?.user) {
      fetchProfile();
    }
  }, [session]);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("secure_profiles")
        .select("*")
        .eq("id", session?.user?.id)
        .single();

      if (error) throw error;

      // Verificar se recebemos uma mensagem de segurança
      if (data.security_message) {
        console.error("Security message:", data.security_message);
        toast({
          variant: "destructive",
          title: "Acesso negado",
          description: data.security_message,
        });
        return;
      }

      setProfile(data);
    } catch (error) {
      console.error("Error fetching profile:", error);
      toast({
        variant: "destructive",
        title: "Erro ao carregar perfil",
        description: "Não foi possível carregar suas informações.",
      });
    } finally {
      setLoading(false);
    }
  };

  const convertToWebP = async (file: File): Promise<Blob> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('Failed to get canvas context'));
          return;
        }
        ctx.drawImage(img, 0, 0);
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to convert image to WebP'));
          }
        }, 'image/webp', 0.8);
      };
      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  };

  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      setUploading(true);
      if (!event.target.files || event.target.files.length === 0) {
        throw new Error("Você precisa selecionar uma imagem para fazer upload.");
      }

      const file = event.target.files[0];
      const webpBlob = await convertToWebP(file);
      const webpFile = new File([webpBlob], `${file.name.split('.')[0]}.webp`, { type: 'image/webp' });

      const filePath = `${session?.user?.id}-${Math.random()}.webp`;

      const { error: uploadError } = await supabase.storage
        .from("avatars")
        .upload(filePath, webpFile);

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from("avatars")
        .getPublicUrl(filePath);

      const { error: updateError } = await supabase
        .from("profiles")
        .update({ avatar_url: publicUrl })
        .eq("id", session?.user?.id);

      if (updateError) throw updateError;

      setProfile({ ...profile, avatar_url: publicUrl });
      toast({
        title: "Avatar atualizado",
        description: "Sua foto de perfil foi atualizada com sucesso.",
      });
    } catch (error: any) {
      console.error("Error uploading avatar:", error);
      toast({
        variant: "destructive",
        title: "Erro ao fazer upload",
        description: "Não foi possível atualizar sua foto de perfil.",
      });
    } finally {
      setUploading(false);
    }
  };

  const handleProfileUpdate = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    try {
      const updateData = {
        full_name: profile.full_name,
        formation_area: profile.formation_area,
        graduation_year: profile.graduation_year,
        is_student: profile.is_student,
        is_professional: profile.is_professional,
        professional_email: profile.professional_email,
        phone: profile.phone,
        registration_number: profile.registration_number,
      };

      const { error } = await supabase
        .from("profiles")
        .update(updateData)
        .eq("id", session?.user?.id);

      if (error) throw error;

      toast({
        title: "Perfil atualizado",
        description: "Suas informações foram atualizadas com sucesso.",
      });

      await fetchProfile();
    } catch (error: any) {
      console.error("Error updating profile:", error);
      toast({
        variant: "destructive",
        title: "Erro ao atualizar perfil",
        description: "Não foi possível atualizar suas informações.",
      });
    }
  };

  if (loading) {
    return (
      <div className={getThemeClasses.pageBackground()}>
        <Header />
        <div className="container mx-auto px-4 py-8 text-center dark:text-gray-300">Carregando...</div>
        <Footer />
      </div>
    );
  }

  return (
    <div className={getThemeClasses.pageBackground()}>
      <HelmetWrapper>
        <title>PedBook | Configurações</title>
        <meta name="description" content="Gerencie suas configurações e perfil no PedBook." />
      </HelmetWrapper>

      <Header />
      <main className="container mx-auto px-4 py-8 relative space-y-6">
        <Link to="/" className="absolute left-4 top-0 hidden sm:flex">
          <Button
            variant="ghost"
            size="sm"
            className="flex items-center gap-2 dark:text-gray-300 dark:hover:bg-slate-800/80"
          >
            <ChevronLeft className="h-4 w-4" />
            Voltar
          </Button>
        </Link>

        <Card className="max-w-2xl mx-auto backdrop-blur-sm bg-white/80 dark:bg-slate-800/90 shadow-xl border-0 dark:shadow-slate-900/30">
          <CardHeader className="text-center">
            <CardTitle className={getThemeClasses.gradientHeading("text-2xl font-bold")}>
              Configurações do Perfil
            </CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-400">
              Gerencie suas informações pessoais e foto de perfil
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-8">
            <div className="flex justify-center">
              <AvatarUpload
                profile={profile}
                session={session}
                uploading={uploading}
                handleAvatarUpload={handleAvatarUpload}
              />
            </div>
            <ProfileForm
              profile={profile}
              setProfile={setProfile}
              handleProfileUpdate={handleProfileUpdate}
            />
          </CardContent>
        </Card>

        <div className="max-w-2xl mx-auto">
          <DeleteAccountForm />
        </div>
      </main>
      <Footer />

      {/* Only render one dialog - moved to the feedback page */}
      <Dialog
        open={showFeedbackDialog}
        onOpenChange={(value) => {
          setShowFeedbackDialog(value);
          // Ensure we don't have any stale state that could cause issues
          if (!value) {
            document.body.style.pointerEvents = 'auto';
          }
        }}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto dark:bg-slate-800">
          <DialogHeader>
            <DialogTitle>Feedback</DialogTitle>
          </DialogHeader>
          <FeedbackPage />
        </DialogContent>
      </Dialog>

      {/* This SiteFeedbackDialog will render itself conditionally based on timer */}
    </div>
  );
};

export default Settings;
