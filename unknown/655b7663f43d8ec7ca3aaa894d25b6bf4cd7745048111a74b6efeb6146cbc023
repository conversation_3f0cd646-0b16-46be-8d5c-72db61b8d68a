import React, { useRef } from "react";
import { Input } from "@/components/ui/input";
import { Camera } from "lucide-react";

interface AvatarUploadProps {
  profile: any;
  session: any;
  uploading: boolean;
  handleAvatarUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const AvatarUpload = ({ profile, session, uploading, handleAvatarUpload }: AvatarUploadProps) => {
  const inputRef = useRef<HTMLInputElement>(null);
  
  const handleClick = () => {
    inputRef.current?.click();
  };

  return (
    <div className="relative w-24 h-24 mx-auto mb-6">
      <div className="w-24 h-24 rounded-full overflow-hidden cursor-pointer transition-transform hover:scale-105" onClick={handleClick}>
        <img
          src={profile?.avatar_url || `https://www.gravatar.com/avatar/${session?.user?.email}?d=mp`}
          alt={profile?.full_name}
          className="w-full h-full object-cover"
          onError={(e) => {
            (e.target as HTMLImageElement).src = "https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/avatars/user.png";
          }}
        />
      </div>
      
      <button
        onClick={handleClick}
        className="absolute bottom-0 right-0 p-2 rounded-full bg-primary text-white shadow-lg hover:bg-primary/90 transition-colors"
        disabled={uploading}
      >
        <Camera className="h-4 w-4" />
      </button>

      <Input
        ref={inputRef}
        type="file"
        accept=".jpg,.jpeg,.png,.gif,.webp"
        onChange={handleAvatarUpload}
        disabled={uploading}
        className="hidden"
      />
    </div>
  );
};

export default AvatarUpload;