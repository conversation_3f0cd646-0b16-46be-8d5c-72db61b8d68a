import { useState } from "react";
import { Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { PatientInfoSection } from "@/components/flowcharts/dka/PatientInfoSection";
import { InitialDiagnosis } from "@/components/flowcharts/dka/stages/InitialDiagnosis";
import { DiagnosticCriteria } from "@/components/flowcharts/dka/stages/DiagnosticCriteria";
import { TreatmentPlan } from "@/components/flowcharts/dka/stages/TreatmentPlan";
import { MaintenancePhase } from "@/components/flowcharts/dka/stages/MaintenancePhase";
import { DKAResult } from "@/components/flowcharts/dka/DKAResult";
import { useWeight } from "@/hooks/useWeight";
import { useAge } from "@/hooks/useAge";

type FlowStage = 'initial' | 'diagnosis' | 'treatment' | 'maintenance' | 'result';

const DKAFlowchart = () => {
  const { weight, setWeight, displayWeight, setTempWeight, commitWeight } = useWeight();
  const { age, setAge, displayAge, setTempAge, commitAge } = useAge();
  
  const [stage, setStage] = useState<FlowStage>('initial');
  const [result, setResult] = useState<any>(null);

  const handleStartAssessment = () => {
    setStage('diagnosis');
  };

  const handleDiagnosisComplete = (isDKA: boolean) => {
    if (!isDKA) {
        setResult({
            severity: null,
            recommendations: [
                "Não há critérios para diagnóstico de CAD. Avaliar outro diagnóstico:",
                "Estado hiperglicêmico hiperosmolar (EHH).",
                "Ingestão de substâncias tóxicas (ex.: metanol, etilenoglicol).",
                "Sepse grave ou choque."
            ]
        });
        setStage('result');
    } else {
        setStage('treatment');
    }
  };

  const handleTreatmentComplete = () => {
    setStage('maintenance');
  };

  const handleMaintenanceComplete = (result: any) => {
    setResult(result);
    setStage('result');
  };

  const resetFlowchart = () => {
    setStage('initial');
    setResult(null);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <HelmetWrapper>
        <title>PedBook | Cetoacidose Diabética</title>
        <meta name="description" content="Fluxograma interativo para manejo de cetoacidose diabética em pediatria" />
      </HelmetWrapper>

      <Header />
      <main className="flex-1 container max-w-2xl mx-auto p-4 space-y-6">
        <div className="flex items-center gap-4">
          <Link 
            to="/flowcharts" 
            className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Voltar para Fluxogramas</span>
          </Link>
        </div>

        <h1 className="text-2xl font-bold text-center">
          Cetoacidose Diabética na Emergência Pediátrica
        </h1>

        {stage === 'initial' && (
          <>
            <PatientInfoSection
              weight={displayWeight}
              onWeightChange={setTempWeight}
              onWeightCommit={commitWeight}
              age={displayAge}
              onAgeChange={setTempAge}
              onAgeCommit={commitAge}
            />
            <InitialDiagnosis onStart={handleStartAssessment} />
          </>
        )}

        {stage === 'diagnosis' && (
          <DiagnosticCriteria onDiagnosisComplete={handleDiagnosisComplete} />
        )}

        {stage === 'treatment' && (
          <TreatmentPlan
            weight={weight}
            onComplete={handleTreatmentComplete}
          />
        )}

        {stage === 'maintenance' && (
          <MaintenancePhase
            weight={weight}
            onComplete={handleMaintenanceComplete}
          />
        )}

        <DKAResult
          result={result}
          showResult={stage === 'result'}
          onReset={resetFlowchart}
          weight={weight}
        />
      </main>
      <Footer />
    </div>
  );
};

export default DKAFlowchart;
