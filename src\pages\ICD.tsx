
import { useState, useEffect } from "react";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { ICDSearchForm } from "@/components/icd/ICDSearchForm";
import { ICDSearchResults } from "@/components/icd/ICDSearchResults";

interface ICDResult {
  code: string;
  name: string;
  description: string | null;
}

export default function ICD() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const queryClient = useQueryClient();

  useEffect(() => {
    const prefetchData = async () => {
      await queryClient.prefetchQuery({
        queryKey: ["icd-search", ""],
        queryFn: fetchICDData,
        staleTime: 12 * 60 * 60 * 1000,
        gcTime: 24 * 60 * 60 * 1000,
      });
    };
    prefetchData();
  }, [queryClient]);

  const fetchICDData = async () => {
    if (!searchTerm || !isSearching) return [];
    
    try {
      const { data: unifiedResults, error } = await supabase
        .from('unified_cids')
        .select('*')
        .or(`name.ilike.%${searchTerm}%,code.ilike.%${searchTerm}%`)
        .limit(50);

      if (error) {
        throw error;
      }

      const formattedResults = unifiedResults?.map(item => ({
        code: item.code,
        name: item.name,
        description: item.description
      })) || [];

      return formattedResults;
    } catch (error) {
      throw error;
    }
  };

  const { data: results, isLoading } = useQuery({
    queryKey: ["icd-search", searchTerm],
    queryFn: fetchICDData,
    staleTime: 12 * 60 * 60 * 1000,
    gcTime: 24 * 60 * 60 * 1000,
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSearching(true);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-white via-primary/5 to-white dark:from-slate-900 dark:via-slate-900 dark:to-slate-800">
      <HelmetWrapper>
        <title>PedBook | CID-10</title>
        <meta name="description" content="Busca rápida e eficiente de códigos CID-10 para pediatria." />
      </HelmetWrapper>

      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-12 max-w-4xl">
        <div className="space-y-8 animate-fade-in">
          <div className="mb-6">
            <Button
              variant="ghost"
              className="text-primary hover:text-primary/80 transition-colors dark:text-primary-foreground dark:hover:text-primary-foreground/80"
              onClick={() => navigate("/")}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar ao Menu
            </Button>
          </div>

          <div className="text-center space-y-6">
            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent dark:from-blue-400 dark:to-blue-300">
              Busca CID-10
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              Digite abaixo a doença para buscar o seu código CID
            </p>
          </div>

          <ICDSearchForm
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            setIsSearching={setIsSearching}
            handleSearch={handleSearch}
          />

          {isSearching && (
            <ICDSearchResults
              results={results || []}
              isLoading={isLoading}
              searchTerm={searchTerm}
            />
          )}
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
