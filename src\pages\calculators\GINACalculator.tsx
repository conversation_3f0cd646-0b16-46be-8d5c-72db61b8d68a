
import { useState } from "react";
import { Link } from "react-router-dom";
import { ChevronLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { GINAMetaTags } from "@/components/calculators/gina/GINAMetaTags";
import { getThemeClasses } from "@/components/ui/theme-utils";

const GINACalculator = () => {
  const [selectedCriteria, setSelectedCriteria] = useState<string[]>([]);

  const criteria = [
    {
      id: "diurnal",
      label: "Apresentou sintomas diurnos mais de 2x por semana",
      description: "Sintomas como tosse, chiado, falta de ar durante o dia"
    },
    {
      id: "nocturnal",
      label: "Apresentou despertar noturno devido à asma",
      description: "Acordou durante a noite por causa dos sintomas"
    },
    {
      id: "reliever",
      label: "Usou beta-agonista de curta duração pelo menos 2x por semana",
      description: "Necessidade de medicação de alívio para os sintomas"
    },
    {
      id: "limitation",
      label: "Apresentou limitação das atividades diárias devido à asma",
      description: "Restrição em atividades normais por causa da asma"
    }
  ];

  const getResult = (score: number) => {
    if (score === 0) {
      return {
        text: "Asma controlada",
        description: "Nenhum critério presente nas últimas 4 semanas",
        color: "text-green-600 dark:text-green-400"
      };
    } else if (score <= 2) {
      return {
        text: "Asma parcialmente controlada",
        description: "1 a 2 critérios presentes nas últimas 4 semanas",
        color: "text-yellow-600 dark:text-yellow-400"
      };
    }
    return {
      text: "Asma não controlada",
      description: "3 a 4 critérios presentes nas últimas 4 semanas",
      color: "text-red-600 dark:text-red-400"
    };
  };

  const handleCriteriaChange = (criteriaId: string, checked: boolean) => {
    setSelectedCriteria(prev => 
      checked 
        ? [...prev, criteriaId]
        : prev.filter(id => id !== criteriaId)
    );
  };

  const score = selectedCriteria.length;
  const result = getResult(score);

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col")}>
      <GINAMetaTags />
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Link to="/calculadoras">
              <Button variant="ghost" size="icon" className="hover:bg-primary/10 dark:hover:bg-primary/20">
                <ChevronLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h1 className={getThemeClasses.gradientHeading("text-3xl")}>
              Controle da Asma (GINA)
            </h1>
          </div>
          
          <p className="text-gray-600 dark:text-gray-300">
            Avaliação do controle da asma baseada nos critérios GINA 2022 considerando os últimos 28 dias
          </p>

          <Card className={getThemeClasses.card("p-6 space-y-6")}>
            {criteria.map((criterion) => (
              <div key={criterion.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50">
                <Checkbox
                  id={criterion.id}
                  checked={selectedCriteria.includes(criterion.id)}
                  onCheckedChange={(checked) => 
                    handleCriteriaChange(criterion.id, checked as boolean)
                  }
                />
                <div className="space-y-1">
                  <Label htmlFor={criterion.id} className="text-base font-medium cursor-pointer text-gray-800 dark:text-gray-200">
                    {criterion.label}
                  </Label>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{criterion.description}</p>
                </div>
              </div>
            ))}

            <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
              <div className="text-center space-y-4">
                <div className="text-4xl font-bold text-primary dark:text-blue-400">
                  {score} critério{score !== 1 ? 's' : ''}
                </div>
                <div className={`text-xl font-semibold ${result.color}`}>
                  {result.text}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto">
                  {result.description}
                  <p className="mt-2 text-xs">
                    Nota: Esta avaliação deve ser considerada em conjunto com outros parâmetros clínicos para decisões terapêuticas.
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default GINACalculator;
