
import React from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowLef<PERSON>, RefreshCw } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { usePhoneutriaFlow } from "@/components/flowcharts/phoneutria/usePhoneutriaFlow";
import { PhoneutriaQuestion } from "@/components/flowcharts/phoneutria/PhoneutriaQuestion";
import { PhoneutriaResult } from "@/components/flowcharts/phoneutria/PhoneutriaResult";
import { ClinicalPicturesGrid } from "@/components/flowcharts/phoneutria/components/ClinicalPicturesGrid";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Stage } from "@/components/flowcharts/phoneutria/types";
import {
  MILD_TREATMENT,
  MODERATE_TREATMENT_UNDER_7,
  M<PERSON><PERSON>ATE_TREATMENT_OVER_7,
  SEVERE_TREATMENT
} from "@/components/flowcharts/phoneutria/constants";

const PhoneutriaFlowchart = () => {
  const { currentStage, setStage, resetFlow } = usePhoneutriaFlow();
  const { toast } = useToast();

  const handleSelectPicture = (severity: 'mild' | 'moderate' | 'severe') => {
    switch (severity) {
      case 'mild':
        setStage('mildTreatment');
        break;
      case 'moderate':
        setStage('moderateQuestion');
        break;
      case 'severe':
        setStage('severeTreatment');
        break;
    }
  };

  const renderStage = () => {
    switch (currentStage) {
      case 'initial':
      case 'unidentifiedSpider':
      case 'identifiedSpider':
        return (
          <PhoneutriaQuestion
            stage={currentStage}
            onAnswer={(answer: Stage) => {
              if (answer === 'discharge') {
                toast({
                  title: "Alta com orientações",
                  description: "Orientar retorno imediato em caso de sintomas",
                });
              }
              setStage(answer);
            }}
          />
        );

      case 'clinicalPictures':
        return <ClinicalPicturesGrid onSelectPicture={handleSelectPicture} />;

      case 'mildTreatment':
        return (
          <PhoneutriaResult
            group="Quadro Leve"
            color="bg-[#F2FCE2] dark:bg-green-900/30"
            instructions={MILD_TREATMENT}
          />
        );

      case 'moderateQuestion':
        return (
          <PhoneutriaResult
            group="Quadro Moderado"
            color="bg-[#FEF7CD] dark:bg-yellow-900/30"
            instructions={[]}
            nextQuestion="Qual a idade do paciente?"
            onContinue={(step: 'under7' | 'over7') => {
              if (step === 'under7') {
                setStage('moderateTreatmentUnder7');
              } else {
                setStage('moderateTreatmentOver7');
              }
            }}
          />
        );

      case 'moderateTreatmentUnder7':
        return (
          <PhoneutriaResult
            group="Quadro Moderado"
            color="bg-[#FEF7CD] dark:bg-yellow-900/30"
            instructions={MODERATE_TREATMENT_UNDER_7}
          />
        );

      case 'moderateTreatmentOver7':
        return (
          <PhoneutriaResult
            group="Quadro Moderado"
            color="bg-[#FEF7CD] dark:bg-yellow-900/30"
            instructions={MODERATE_TREATMENT_OVER_7}
          />
        );

      case 'severeTreatment':
        return (
          <PhoneutriaResult
            group="Quadro Grave"
            color="bg-[#FFDEE2] dark:bg-red-900/30"
            instructions={SEVERE_TREATMENT}
          />
        );

      case 'discharge':
        return (
          <Card className="p-8 max-w-2xl mx-auto bg-white dark:bg-slate-800 shadow-md border-2 border-gray-100 dark:border-gray-700">
            <h2 className="text-2xl font-bold mb-6 text-gray-800 dark:text-gray-100">Alta do Paciente</h2>
            <div className="space-y-6">
              <div className="bg-[#F2FCE2] dark:bg-green-900/30 p-6 rounded-lg border-2 border-[#E8F7D4] dark:border-green-800/50">
                <p className="text-gray-700 dark:text-gray-200">
                  Paciente estável. Orientar retorno imediato caso surjam novos sintomas.
                </p>
              </div>
            </div>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-orange-50 via-white to-orange-50 dark:from-orange-900/20 dark:via-slate-900 dark:to-orange-900/10">
      <HelmetWrapper>
        <title>PedBook | Acidente Fonêutrico</title>
        <meta
          name="description"
          content="Fluxograma para manejo de acidentes com aranha armadeira em pediatria"
        />
      </HelmetWrapper>

      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto space-y-8">
          <div className="flex items-center justify-between gap-4">
            <Link
              to="/flowcharts/venomous"
              className="hidden sm:inline-flex items-center gap-2 text-orange-600 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Voltar para Animais Peçonhentos</span>
            </Link>
            
            <Button
              onClick={resetFlow}
              variant="outline"
              className="flex items-center gap-2 text-orange-600 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 border-orange-200 dark:border-orange-700/50 hover:bg-orange-50 dark:hover:bg-orange-900/20"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Reiniciar</span>
            </Button>
          </div>

          <div className="text-center space-y-4">
            <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-orange-600 to-amber-600 dark:from-orange-400 dark:to-amber-400">
              Acidente Fonêutrico (Aranha Armadeira)
            </h1>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Fluxograma para manejo de acidentes com aranha armadeira em pediatria
            </p>
          </div>

          {renderStage()}

          <div className="mt-8 p-6 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">Referência</h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              BRASIL. Ministério da Saúde. Secretaria de Vigilância em Saúde e Ambiente. Departamento de Doenças Transmissíveis. Guia de Animais Peçonhentos do Brasil. Brasília: Ministério da Saúde, 2024.
            </p>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default PhoneutriaFlowchart;
