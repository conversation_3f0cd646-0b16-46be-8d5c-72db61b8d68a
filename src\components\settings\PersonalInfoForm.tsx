import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Building2, GraduationCap, User, Mail, Phone } from "lucide-react";
import { Button } from "@/components/ui/button";

interface PersonalInfoFormProps {
  profile: any;
  setProfile: (profile: any) => void;
  handleProfileUpdate: (e: React.FormEvent<HTMLFormElement>) => void;
}

const PersonalInfoForm = ({ profile, setProfile, handleProfileUpdate }: PersonalInfoFormProps) => {
  return (
    <form onSubmit={handleProfileUpdate} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Informações Pessoais e Profissionais</CardTitle>
          <CardDescription>
            Mantenha seus dados pessoais e profissionais atualizados.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="full_name" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Nome completo
            </Label>
            <Input
              id="full_name"
              value={profile?.full_name || ''}
              onChange={(e) =>
                setProfile({ ...profile, full_name: e.target.value })
              }
              placeholder="Seu nome completo"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email" className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Email profissional
            </Label>
            <Input
              id="email"
              type="email"
              value={profile?.professional_email || ''}
              onChange={(e) =>
                setProfile({ ...profile, professional_email: e.target.value })
              }
              placeholder="<EMAIL>"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone" className="flex items-center gap-2">
              <Phone className="h-4 w-4" />
              Telefone
            </Label>
            <Input
              id="phone"
              type="tel"
              value={profile?.phone || ''}
              onChange={(e) =>
                setProfile({ ...profile, phone: e.target.value })
              }
              placeholder="(00) 00000-0000"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="formation_area" className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Área de formação
            </Label>
            <Select
              value={profile?.formation_area || ''}
              onValueChange={(value) =>
                setProfile({ ...profile, formation_area: value })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione sua área" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="medicina">Medicina</SelectItem>
                <SelectItem value="enfermagem">Enfermagem</SelectItem>
                <SelectItem value="farmacia">Farmácia</SelectItem>
                <SelectItem value="fisioterapia">Fisioterapia</SelectItem>
                <SelectItem value="nutricao">Nutrição</SelectItem>
                <SelectItem value="outro">Outro</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="graduation_year" className="flex items-center gap-2">
              <GraduationCap className="h-4 w-4" />
              Ano de formação
            </Label>
            <Input
              id="graduation_year"
              type="text"
              value={profile?.graduation_year || ''}
              onChange={(e) =>
                setProfile({ ...profile, graduation_year: e.target.value })
              }
              placeholder="Ex: 2024"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="registration_number" className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Número de registro profissional
            </Label>
            <Input
              id="registration_number"
              value={profile?.registration_number || ''}
              onChange={(e) =>
                setProfile({ ...profile, registration_number: e.target.value })
              }
              placeholder="CRM/COREN/etc"
            />
          </div>

          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="flex items-center justify-between space-x-2">
              <Label htmlFor="is_student">Sou estudante</Label>
              <Switch
                id="is_student"
                checked={profile?.is_student || false}
                onCheckedChange={(checked) =>
                  setProfile({ ...profile, is_student: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between space-x-2">
              <Label htmlFor="is_professional">Sou profissional</Label>
              <Switch
                id="is_professional"
                checked={profile?.is_professional || false}
                onCheckedChange={(checked) =>
                  setProfile({ ...profile, is_professional: checked })
                }
              />
            </div>
          </div>

          <div className="pt-4">
            <Button type="submit" className="w-full sm:w-auto">
              Salvar alterações
            </Button>
          </div>
        </CardContent>
      </Card>
    </form>
  );
};

export default PersonalInfoForm;